# 消息幂等性设计

## 什么是消息幂等性

### 定义
幂等性是指同一个操作执行多次和执行一次的效果相同，不会因为重复执行而产生副作用。

### 为什么需要幂等性
- **网络重传**：网络异常导致消息重复发送
- **消费者重启**：消费者重启后重新消费未确认的消息
- **系统故障**：系统故障恢复后重复处理消息
- **负载均衡**：消息被分发到多个消费者实例

## 常见的非幂等场景

### 1. 数据库操作
```go
// 非幂等：重复执行会导致数据错误
func UpdateUserBalance(userID string, amount float64) error {
    // 每次执行都会增加余额
    _, err := db.Exec("UPDATE users SET balance = balance + ? WHERE id = ?", amount, userID)
    return err
}
```

### 2. 外部API调用
```go
// 非幂等：重复调用会产生多次扣费
func ChargeUser(userID string, amount float64) error {
    return paymentAPI.Charge(userID, amount)
}
```

### 3. 消息发送
```go
// 非幂等：重复执行会发送多条消息
func SendNotification(userID string, message string) error {
    return smsService.Send(userID, message)
}
```

## 幂等性实现方案

### 1. 唯一ID去重
**原理**：为每条消息分配唯一ID，处理前检查是否已处理过

```go
type MessageProcessor struct {
    processedIDs sync.Map // 已处理的消息ID
    db          *sql.DB
}

func (mp *MessageProcessor) ProcessMessage(msg *Message) error {
    // 检查消息是否已处理
    if mp.isProcessed(msg.ID) {
        log.Printf("Message %s already processed, skipping", msg.ID)
        return nil
    }
    
    // 处理消息
    if err := mp.handleMessage(msg); err != nil {
        return err
    }
    
    // 标记为已处理
    mp.markProcessed(msg.ID)
    return nil
}

func (mp *MessageProcessor) isProcessed(messageID string) bool {
    // 先检查内存缓存
    if _, exists := mp.processedIDs.Load(messageID); exists {
        return true
    }
    
    // 检查数据库
    var count int
    err := mp.db.QueryRow("SELECT COUNT(*) FROM processed_messages WHERE message_id = ?", messageID).Scan(&count)
    return err == nil && count > 0
}

func (mp *MessageProcessor) markProcessed(messageID string) {
    mp.processedIDs.Store(messageID, true)
    mp.db.Exec("INSERT INTO processed_messages (message_id, processed_at) VALUES (?, ?)", messageID, time.Now())
}
```

### 2. 数据库唯一约束
**原理**：利用数据库唯一索引防止重复插入

```go
type Order struct {
    ID       string  `json:"id"`
    UserID   string  `json:"user_id"`
    Amount   float64 `json:"amount"`
    Status   string  `json:"status"`
}

func CreateOrder(order *Order) error {
    // 使用订单ID作为主键，重复插入会失败
    _, err := db.Exec(`
        INSERT INTO orders (id, user_id, amount, status, created_at) 
        VALUES (?, ?, ?, ?, ?)
    `, order.ID, order.UserID, order.Amount, order.Status, time.Now())
    
    if isDuplicateKeyError(err) {
        log.Printf("Order %s already exists, skipping", order.ID)
        return nil // 幂等处理
    }
    
    return err
}

func isDuplicateKeyError(err error) bool {
    // 检查是否是重复键错误
    return strings.Contains(err.Error(), "Duplicate entry")
}
```

### 3. 状态机设计
**原理**：通过状态转换确保操作的幂等性

```go
type OrderStatus string

const (
    OrderPending   OrderStatus = "pending"
    OrderPaid      OrderStatus = "paid"
    OrderShipped   OrderStatus = "shipped"
    OrderCompleted OrderStatus = "completed"
)

func ProcessPayment(orderID string) error {
    // 获取当前订单状态
    order, err := getOrder(orderID)
    if err != nil {
        return err
    }
    
    // 状态检查，确保幂等性
    switch order.Status {
    case OrderPaid, OrderShipped, OrderCompleted:
        log.Printf("Order %s already paid, skipping", orderID)
        return nil // 已支付，幂等返回
    case OrderPending:
        // 执行支付逻辑
        if err := chargeUser(order.UserID, order.Amount); err != nil {
            return err
        }
        
        // 更新状态
        return updateOrderStatus(orderID, OrderPaid)
    default:
        return fmt.Errorf("invalid order status: %s", order.Status)
    }
}
```

### 4. 分布式锁
**原理**：使用分布式锁确保同一时间只有一个实例处理消息

```go
type DistributedLock struct {
    redis *redis.Client
}

func (dl *DistributedLock) ProcessWithLock(key string, handler func() error) error {
    lockKey := "lock:" + key
    lockValue := generateUUID()
    
    // 尝试获取锁
    acquired, err := dl.redis.SetNX(lockKey, lockValue, 30*time.Second).Result()
    if err != nil {
        return err
    }
    
    if !acquired {
        log.Printf("Failed to acquire lock for %s, skipping", key)
        return nil // 其他实例正在处理
    }
    
    defer func() {
        // 释放锁
        dl.releaseLock(lockKey, lockValue)
    }()
    
    // 执行业务逻辑
    return handler()
}

func (dl *DistributedLock) releaseLock(key, value string) {
    script := `
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
    `
    dl.redis.Eval(script, []string{key}, value)
}
```

### 5. 版本号控制
**原理**：使用版本号进行乐观锁控制

```go
type User struct {
    ID      string  `json:"id"`
    Balance float64 `json:"balance"`
    Version int     `json:"version"`
}

func UpdateUserBalance(userID string, amount float64) error {
    for retries := 0; retries < 3; retries++ {
        // 获取当前用户信息
        user, err := getUser(userID)
        if err != nil {
            return err
        }
        
        // 更新余额，同时检查版本号
        result, err := db.Exec(`
            UPDATE users 
            SET balance = ?, version = version + 1 
            WHERE id = ? AND version = ?
        `, user.Balance+amount, userID, user.Version)
        
        if err != nil {
            return err
        }
        
        rowsAffected, _ := result.RowsAffected()
        if rowsAffected > 0 {
            return nil // 更新成功
        }
        
        // 版本冲突，重试
        time.Sleep(time.Duration(retries*100) * time.Millisecond)
    }
    
    return fmt.Errorf("failed to update after retries")
}
```

## 最佳实践

### 1. 消息设计原则
```go
type IdempotentMessage struct {
    ID        string    `json:"id"`         // 全局唯一ID
    Type      string    `json:"type"`       // 消息类型
    Payload   string    `json:"payload"`    // 消息内容
    Timestamp time.Time `json:"timestamp"`  // 时间戳
    Version   string    `json:"version"`    // 消息版本
}

// 生成幂等消息
func NewIdempotentMessage(msgType, payload string) *IdempotentMessage {
    return &IdempotentMessage{
        ID:        generateUUID(),
        Type:      msgType,
        Payload:   payload,
        Timestamp: time.Now(),
        Version:   "1.0",
    }
}
```

### 2. 处理器模式
```go
type IdempotentHandler interface {
    GetKey(msg *Message) string
    Process(msg *Message) error
}

type IdempotentProcessor struct {
    handlers map[string]IdempotentHandler
    storage  IdempotentStorage
}

func (ip *IdempotentProcessor) Handle(msg *Message) error {
    handler, exists := ip.handlers[msg.Type]
    if !exists {
        return fmt.Errorf("no handler for message type: %s", msg.Type)
    }
    
    key := handler.GetKey(msg)
    
    // 检查是否已处理
    if ip.storage.IsProcessed(key) {
        return nil
    }
    
    // 处理消息
    if err := handler.Process(msg); err != nil {
        return err
    }
    
    // 标记已处理
    return ip.storage.MarkProcessed(key)
}
```

### 3. 监控和告警
```go
type IdempotentMetrics struct {
    TotalMessages     int64 `json:"total_messages"`
    DuplicateMessages int64 `json:"duplicate_messages"`
    ProcessedMessages int64 `json:"processed_messages"`
    FailedMessages    int64 `json:"failed_messages"`
}

func (ip *IdempotentProcessor) GetMetrics() *IdempotentMetrics {
    return &IdempotentMetrics{
        TotalMessages:     atomic.LoadInt64(&ip.totalMessages),
        DuplicateMessages: atomic.LoadInt64(&ip.duplicateMessages),
        ProcessedMessages: atomic.LoadInt64(&ip.processedMessages),
        FailedMessages:    atomic.LoadInt64(&ip.failedMessages),
    }
}
```

## 面试要点

### Q1: 如何设计幂等的消息处理系统？
- **唯一标识**：每条消息分配全局唯一ID
- **去重机制**：处理前检查是否已处理
- **状态管理**：使用状态机确保操作幂等
- **异常处理**：重复消息直接返回成功

### Q2: 幂等性实现的性能考虑？
- **缓存优化**：使用内存缓存减少数据库查询
- **批量操作**：批量检查和标记消息状态
- **过期清理**：定期清理过期的处理记录
- **分片存储**：大量消息时使用分片存储

### Q3: 如何处理幂等性实现的复杂性？
- **业务层面**：设计天然幂等的业务逻辑
- **框架封装**：提供通用的幂等处理框架
- **配置化**：通过配置控制幂等策略
- **监控告警**：监控重复消息比例

## 总结

消息幂等性的核心要点：
1. **唯一标识**：消息必须有全局唯一标识
2. **去重检查**：处理前检查是否重复
3. **状态管理**：合理设计状态转换
4. **性能优化**：平衡准确性和性能
5. **监控告警**：及时发现异常情况
