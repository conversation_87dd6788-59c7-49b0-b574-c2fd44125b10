# RabbitMQ消息队列详解

## 核心概念

### 基本组件
- **Producer**：消息生产者，发送消息
- **Consumer**：消息消费者，接收消息
- **Queue**：消息队列，存储消息
- **Exchange**：交换机，路由消息到队列
- **Binding**：绑定，连接Exchange和Queue的规则
- **Routing Key**：路由键，消息路由的匹配条件

### AMQP协议特点
- **可靠性**：消息确认机制保证不丢失
- **安全性**：支持认证和权限控制
- **互操作性**：跨语言、跨平台
- **事务支持**：保证消息操作的原子性

## Exchange类型详解

### 1. Direct Exchange（直接交换机）
- **路由规则**：精确匹配routing key
- **使用场景**：点对点消息传递
- **特点**：一对一的消息路由

```go
// 声明Direct Exchange
channel.ExchangeDeclare("direct_exchange", "direct", true, false, false, false, nil)

// 绑定队列：routing key完全匹配
channel.QueueBind("user_queue", "user.created", "direct_exchange", false, nil)
```

### 2. Topic Exchange（主题交换机）
- **路由规则**：模式匹配routing key
- **通配符**：`*`匹配一个单词，`#`匹配零个或多个单词
- **使用场景**：发布订阅模式

```go
// 绑定模式示例
channel.QueueBind("user_queue", "*.user.*", "topic_exchange", false, nil)
// 匹配：create.user.profile, update.user.info
```

### 3. Fanout Exchange（扇出交换机）
- **路由规则**：忽略routing key，广播到所有绑定队列
- **使用场景**：消息广播、系统通知
- **特点**：一对多的消息分发

### 4. Headers Exchange（头部交换机）
- **路由规则**：基于消息头属性匹配
- **匹配模式**：all（全部匹配）或any（任意匹配）
- **使用场景**：复杂的路由条件

## 消息可靠性保证

### 三重保障机制
1. **消息持久化**：消息存储到磁盘
2. **发布确认**：生产者确认消息已接收
3. **消费者确认**：消费者确认消息已处理

### 消息持久化
```go
// 队列持久化 + 消息持久化
queue, err := channel.QueueDeclare("persistent_queue", true, false, false, false, nil)

err = channel.Publish("", queue.Name, false, false, amqp.Publishing{
    DeliveryMode: amqp.Persistent, // 消息持久化
    ContentType:  "text/plain",
    Body:         []byte("Hello World"),
})
```

### 发布确认模式
```go
// 启用确认模式
channel.Confirm(false)

// 发送消息后等待确认
channel.Publish("exchange", "routing.key", false, false, amqp.Publishing{
    ContentType: "text/plain",
    Body:        []byte("message"),
})

// 监听确认结果
confirmed := channel.NotifyPublish(make(chan amqp.Confirmation, 1))
if confirm := <-confirmed; confirm.Ack {
    log.Println("Message confirmed")
}
```

### 消费者确认
```go
// 手动确认模式
msgs, err := channel.Consume(queue.Name, "", false, false, false, false, nil)

for msg := range msgs {
    if err := processMessage(msg.Body); err != nil {
        msg.Nack(false, true) // 拒绝并重新入队
    } else {
        msg.Ack(false) // 确认消息
    }
}
```

## 高级特性

### 死信队列（DLX）
**触发条件**：
- 消息被拒绝且不重新入队
- 消息TTL过期
- 队列达到最大长度

**实现原理**：
```go
// 主队列配置死信交换机
args := amqp.Table{
    "x-dead-letter-exchange":    "dlx_exchange",
    "x-dead-letter-routing-key": "failed",
    "x-message-ttl":             30000, // 30秒TTL
}

channel.QueueDeclare("main_queue", true, false, false, false, args)

// 死信队列处理失败消息
channel.ExchangeDeclare("dlx_exchange", "direct", true, false, false, false, nil)
channel.QueueDeclare("dlx_queue", true, false, false, false, nil)
channel.QueueBind("dlx_queue", "failed", "dlx_exchange", false, nil)
```

### 延迟队列
**实现方式**：TTL + 死信队列
**应用场景**：订单超时取消、定时任务

```go
// 延迟消息实现
func SendDelayedMessage(channel *amqp.Channel, message string, delay time.Duration) error {
    args := amqp.Table{
        "x-message-ttl":             int64(delay / time.Millisecond),
        "x-dead-letter-exchange":    "delayed_exchange",
        "x-dead-letter-routing-key": "process",
    }

    // 创建临时队列
    tempQueue, _ := channel.QueueDeclare("", false, true, true, false, args)

    return channel.Publish("", tempQueue.Name, false, false, amqp.Publishing{
        ContentType: "text/plain",
        Body:        []byte(message),
    })
}
```

### 优先级队列
**使用场景**：VIP用户消息、紧急任务处理

```go
// 声明优先级队列
args := amqp.Table{"x-max-priority": 10}
channel.QueueDeclare("priority_queue", true, false, false, false, args)

// 发送高优先级消息
channel.Publish("", "priority_queue", false, false, amqp.Publishing{
    ContentType: "text/plain",
    Body:        []byte("High priority message"),
    Priority:    8, // 优先级0-10
})
```

## 集群与高可用

### 集群模式
- **普通集群**：元数据同步，消息存储在单个节点
- **镜像队列**：消息在多个节点备份
- **仲裁队列**：基于Raft算法的高可用队列

### 集群配置要点
```bash
# 加入集群
rabbitmqctl join_cluster rabbit@node1

# 设置镜像队列策略
rabbitmqctl set_policy ha-all "^ha\." '{"ha-mode":"all"}'

# 查看集群状态
rabbitmqctl cluster_status
```

### 高可用策略
- **负载均衡**：多个节点分担负载
- **故障转移**：主节点故障时自动切换
- **数据备份**：定期备份队列数据
- **监控告警**：实时监控集群状态

## 性能优化

### 连接管理
- **连接复用**：使用连接池减少连接开销
- **Channel复用**：一个连接创建多个Channel
- **心跳设置**：合理设置心跳间隔

```go
// 简化的连接池
type ConnectionPool struct {
    connections chan *amqp.Connection
    maxSize     int
}

func (p *ConnectionPool) Get() (*amqp.Connection, error) {
    select {
    case conn := <-p.connections:
        return conn, nil
    default:
        return amqp.Dial("amqp://localhost")
    }
}
```

### 消息优化
- **批量发送**：减少网络往返次数
- **消息压缩**：减少网络传输量
- **预取设置**：控制消费者预取消息数量

### 队列优化
- **队列分片**：避免单个队列过大
- **惰性队列**：消息直接存储到磁盘
- **队列长度限制**：防止内存溢出

## 面试高频问题

### Q1: RabbitMQ的Exchange类型及使用场景？
- **Direct**：精确匹配routing key，用于点对点通信
- **Topic**：模式匹配routing key，用于发布订阅
- **Fanout**：广播消息，用于系统通知
- **Headers**：基于消息头路由，用于复杂路由条件

### Q2: 如何保证消息不丢失？
- **生产者**：开启发布确认模式
- **队列**：设置队列和消息持久化
- **消费者**：手动确认消息处理完成
- **集群**：使用镜像队列备份消息

### Q3: 死信队列的应用场景？
- **消息重试**：处理失败的消息
- **延迟队列**：实现定时任务
- **消息审计**：记录异常消息
- **流量削峰**：缓存过期消息

### Q4: RabbitMQ vs Kafka的选择？
| 特性 | RabbitMQ | Kafka |
|------|----------|-------|
| 延迟 | 低延迟(微秒级) | 中等延迟(毫秒级) |
| 吞吐量 | 中等 | 高吞吐量 |
| 路由 | 灵活的路由机制 | 简单的topic分区 |
| 消息顺序 | 队列内有序 | 分区内有序 |
| 持久化 | 可选持久化 | 默认持久化 |

### Q5: 如何处理消息积压？
- **增加消费者**：水平扩展消费能力
- **批量消费**：提高消费效率
- **优化处理逻辑**：减少单条消息处理时间
- **消息过滤**：在消费端过滤不必要的消息

### Q6: 消息幂等性如何保证？
- **业务层面**：设计幂等的业务逻辑
- **消息去重**：使用消息ID去重
- **数据库约束**：利用唯一索引防重
- **分布式锁**：处理前先获取锁

## 总结

RabbitMQ核心优势：
1. **灵活路由**：多种Exchange类型支持复杂路由
2. **可靠传输**：完善的消息确认机制
3. **高可用**：集群和镜像队列保证可用性
4. **易管理**：丰富的管理工具和监控指标
5. **生态完善**：多语言客户端支持
