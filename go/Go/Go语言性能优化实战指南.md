# Go语言性能优化实战指南

## 性能优化基础

### 1. 性能分析工具

#### pprof性能分析
```go
import _ "net/http/pprof"

func main() {
    // 启动pprof服务
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    // 业务代码...
}
```

**常用pprof命令：**
- `go tool pprof http://localhost:6060/debug/pprof/profile` - CPU分析
- `go tool pprof http://localhost:6060/debug/pprof/heap` - 内存分析
- `go tool pprof http://localhost:6060/debug/pprof/goroutine` - 协程分析

#### 基准测试
```go
// 字符串拼接性能对比
func BenchmarkStringConcat(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var result string
        for j := 0; j < 100; j++ {
            result += "hello"  // 性能差
        }
    }
}

func BenchmarkStringBuilder(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var builder strings.Builder
        for j := 0; j < 100; j++ {
            builder.WriteString("hello")  // 性能好
        }
        _ = builder.String()
    }
}
```

### 2. 内存优化

#### 对象池模式
```go
var bufferPool = sync.Pool{
    New: func() interface{} {
        return &Buffer{data: make([]byte, 0, 1024)}
    },
}

func processData(input []byte) []byte {
    buf := bufferPool.Get().(*Buffer)
    defer func() {
        buf.Reset()
        bufferPool.Put(buf)
    }()

    buf.data = append(buf.data, input...)
    result := make([]byte, len(buf.data))
    copy(result, buf.data)
    return result
}
```

**优势：** 减少内存分配，降低GC压力

#### 内存预分配
```go
// 差：频繁扩容
func badSlice(n int) []int {
    var result []int
    for i := 0; i < n; i++ {
        result = append(result, i)
    }
    return result
}

// 好：预分配容量
func goodSlice(n int) []int {
    result := make([]int, 0, n)
    for i := 0; i < n; i++ {
        result = append(result, i)
    }
    return result
}

// 最好：直接索引赋值
func bestSlice(n int) []int {
    result := make([]int, n)
    for i := 0; i < n; i++ {
        result[i] = i
    }
    return result
}
```

### 3. CPU优化

#### 缓存优化
```go
type Calculator struct {
    cache map[int]int
}

func (c *Calculator) ExpensiveCalculation(n int) int {
    if result, exists := c.cache[n]; exists {
        return result  // 缓存命中
    }

    result := heavyComputation(n)
    c.cache[n] = result
    return result
}
```

#### 并发优化
```go
// 工作池模式
func processDataConcurrent(data []int) []int {
    numWorkers := runtime.NumCPU()
    jobs := make(chan int, len(data))
    results := make(chan int, len(data))

    // 启动工作者
    var wg sync.WaitGroup
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for job := range jobs {
                results <- expensiveOperation(job)
            }
        }()
    }

    // 发送任务
    go func() {
        for _, v := range data {
            jobs <- v
        }
        close(jobs)
    }()

    // 收集结果
    go func() {
        wg.Wait()
        close(results)
    }()

    var output []int
    for result := range results {
        output = append(output, result)
    }
    return output
}
```

### 4. I/O优化

#### 缓冲I/O
```go
// 使用缓冲读取
func readFileBuffered(filename string) ([]byte, error) {
    file, err := os.Open(filename)
    if err != nil {
        return nil, err
    }
    defer file.Close()

    reader := bufio.NewReader(file)  // 使用缓冲
    return io.ReadAll(reader)
}

// 使用缓冲写入
func writeDataBuffered(filename string, data [][]byte) error {
    file, err := os.Create(filename)
    if err != nil {
        return err
    }
    defer file.Close()

    writer := bufio.NewWriter(file)  // 使用缓冲
    defer writer.Flush()

    for _, chunk := range data {
        writer.Write(chunk)
    }
    return nil
}
```

### 5. 网络优化

#### 连接池
```go
// HTTP客户端优化
func createOptimizedHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableKeepAlives:   false,  // 启用keep-alive
    }

    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}
```

## 面试常见问题

### Q1: Go程序性能瓶颈通常在哪里？

**答案**：
1. **内存分配**：频繁的小对象分配导致GC压力
2. **字符串操作**：大量字符串拼接操作
3. **反射使用**：过度使用反射影响性能
4. **锁竞争**：不合理的锁使用导致竞争
5. **I/O阻塞**：同步I/O操作阻塞goroutine

### Q2: 如何减少GC压力？

**答案**：
1. **对象池**：复用对象减少分配
2. **预分配**：提前分配足够的容量
3. **避免装箱**：减少interface{}的使用
4. **结构体优化**：合理安排字段顺序
5. **及时释放**：主动设置大对象为nil

### Q3: 什么时候使用指针，什么时候使用值？

**答案**：
- **使用指针**：大结构体、需要修改、避免拷贝
- **使用值**：小结构体、不可变数据、简单类型
- **性能考虑**：指针可能增加GC压力，值拷贝可能影响性能

### Q4: 如何优化字符串操作？

**答案**：
1. **strings.Builder**：高效的字符串构建
2. **预分配容量**：避免多次扩容
3. **避免+操作**：大量拼接时使用Builder
4. **字节切片**：直接操作[]byte
5. **字符串池**：复用常用字符串

## 性能优化最佳实践

1. **先测量再优化**：使用pprof等工具定位瓶颈
2. **避免过早优化**：在正确性基础上优化
3. **基准测试**：量化优化效果
4. **内存友好**：减少分配，复用对象
5. **并发合理**：避免过度并发和锁竞争
6. **I/O优化**：使用缓冲，批量操作
7. **算法优先**：选择合适的算法和数据结构
