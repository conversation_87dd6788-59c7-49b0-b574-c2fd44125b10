# 进程调度算法详解

## 调度基础概念

### 调度层次
- **作业调度**：决定哪些作业进入系统
- **内存调度**：决定进程的换入换出
- **进程调度**：决定哪个就绪进程获得CPU

### 调度分类
**抢占式 vs 非抢占式**：
- 抢占式：可强制收回CPU
- 非抢占式：进程主动放弃CPU

**调度目标**：
- 批处理系统：吞吐量、周转时间
- 交互式系统：响应时间、公平性
- 实时系统：满足截止时间

## 经典调度算法

### 1. 先来先服务（FCFS）
**原理**：按进程到达顺序调度，非抢占式

**特点**：
- 优点：简单公平，无饥饿
- 缺点：平均等待时间长，护航效应

**实现要点**：
- 按到达时间排序进程
- 依次执行，不可抢占
- 等待时间 = 开始时间 - 到达时间
- 周转时间 = 等待时间 + 执行时间

### 2. 最短作业优先（SJF）
**原理**：选择执行时间最短的进程

**分类**：
- 非抢占式SJF：作业开始后不能被抢占
- 抢占式SJF（SRTF）：可被剩余时间更短的进程抢占

**特点**：
- 优点：平均等待时间最短
- 缺点：可能导致长作业饥饿，需要预知执行时间

### 3. 优先级调度
**原理**：根据进程优先级进行调度

**分类**：
- 静态优先级：优先级固定不变
- 动态优先级：优先级可以改变

**老化机制**：防止低优先级进程饥饿
- 随着等待时间增加，逐渐提高优先级

### 3. 优先级调度

**静态优先级**：
- 优先级固定不变
- 选择优先级最高的就绪进程执行
- 可能导致低优先级进程饥饿

**动态优先级（老化机制）**：
- 随等待时间增加，逐渐提高优先级
- 防止饥饿问题
- 实现：`新优先级 = 原优先级 + 等待时间/时间常数`

### 4. 轮转调度（RR）

**基本原理**：
- 每个进程分配固定时间片（如10ms）
- 时间片用完后切换到下一个进程
- 被抢占的进程放入就绪队列尾部

**时间片选择**：
- 太小：上下文切换开销大
- 太大：响应时间差，退化为FCFS
- 一般选择10-100ms

### 5. 多级反馈队列

**基本思想**：
- 设置多个优先级队列（如3级）
- 新进程进入最高优先级队列
- 时间片用完降级到下一级队列
- 高优先级队列优先调度

**队列特点**：
- 第1级：时间片小（如8ms），响应快
- 第2级：时间片中等（如16ms）
- 第3级：FCFS调度，时间片无限

**优点**：兼顾短作业和长作业，响应时间好

## 实时调度算法

### 1. 速率单调调度（RMS）

**基本原理**：
- 周期越短，优先级越高
- 静态优先级，抢占式调度
- 适用于周期性实时任务

**可调度性判断**：
- CPU利用率 ≤ n×(2^(1/n) - 1)
- n为任务数，当n→∞时，界限约为69.3%

**特点**：
- 优点：简单，理论基础完善
- 缺点：利用率不高，只适用于周期任务

### 2. 最早截止时间优先（EDF）

**基本原理**：
- 截止时间越早，优先级越高
- 动态优先级，抢占式调度
- 适用于周期性和非周期性任务

**可调度性判断**：
- CPU利用率 ≤ 100%（理论最优）
- 利用率 = Σ(执行时间/周期)

**特点**：
- 优点：CPU利用率最高，理论最优
- 缺点：实现复杂，优先级频繁变化

## 调度算法性能分析

### 1. 性能指标

**主要指标**：
- **等待时间**：进程在就绪队列中等待的时间
- **周转时间**：从进程提交到完成的总时间
- **响应时间**：从提交到首次响应的时间
- **CPU利用率**：CPU忙碌时间占总时间的比例
- **吞吐量**：单位时间内完成的进程数

### 2. 算法比较

| 算法 | 平均等待时间 | 响应时间 | 公平性 | 饥饿问题 | 适用场景 |
|------|--------------|----------|--------|----------|----------|
| FCFS | 长 | 差 | 好 | 无 | 批处理 |
| SJF | 最优 | 差 | 差 | 有 | 批处理 |
| 优先级 | 中等 | 好 | 差 | 有 | 实时系统 |
| RR | 中等 | 好 | 好 | 无 | 交互式 |
| 多级反馈 | 中等 | 很好 | 好 | 无 | 通用 |

## 现代调度算法

### 1. 完全公平调度器（CFS）

**核心思想**：
- 每个进程维护虚拟运行时间（vruntime）
- 总是选择vruntime最小的进程执行
- 使用红黑树维护就绪队列，O(log n)复杂度

**虚拟时间计算**：
- `vruntime += 实际运行时间 × 1024 / 权重`
- nice值越小，权重越大，vruntime增长越慢
- 实现进程间的公平调度

### 2. 多核调度

**负载均衡策略**：
- **推迁移**：忙碌CPU主动推送任务到空闲CPU
- **拉迁移**：空闲CPU主动从忙碌CPU拉取任务
- **周期性均衡**：定期检查各CPU负载差异

**关键考虑**：
- **CPU亲和性**：尽量避免任务在CPU间频繁迁移
- **NUMA感知**：优先在同一NUMA节点内调度
- **缓存局部性**：考虑L1/L2缓存的影响

## 高频面试问题

### Q1: 抢占式和非抢占式调度的区别？
**抢占式调度**：
- 可以强制收回正在执行进程的CPU
- 响应时间好，适合交互式系统
- 需要时钟中断支持，开销较大

**非抢占式调度**：
- 进程主动放弃CPU才能切换
- 实现简单，开销小
- 可能导致响应时间差

### Q2: 为什么需要多级反馈队列？
**解决问题**：
- 区分交互式进程和CPU密集型进程
- 给短作业更好的响应时间
- 防止长作业饥饿

**工作原理**：
- 新进程进入最高优先级队列
- 时间片用完降级，体现进程特性
- 长时间未执行的进程可以提升优先级

### Q3: 实时调度和普通调度的区别？
**实时调度特点**：
- 必须满足截止时间约束
- 可预测性比平均性能更重要
- 需要可调度性分析

**调度算法**：
- RMS：周期越短优先级越高
- EDF：截止时间越早优先级越高

### Q4: 如何解决优先级反转问题？
**问题描述**：高优先级任务被低优先级任务阻塞

**解决方案**：
- **优先级继承**：低优先级任务临时继承高优先级
- **优先级天花板**：访问共享资源时提升到最高优先级
- **避免共享资源**：使用消息传递代替共享内存

### Q5: Linux CFS调度器的优势？
**主要优势**：
- 完全公平，避免饥饿
- O(log n)时间复杂度
- 支持多核负载均衡
- 自适应时间片

**核心机制**：
- 虚拟运行时间保证公平性
- 红黑树高效维护就绪队列
- nice值影响权重和调度

## 面试要点总结

1. **基本概念**：理解调度的目标和分类
2. **经典算法**：掌握FCFS、SJF、优先级、RR等算法的原理
3. **实时调度**：了解RMS和EDF算法的特点和适用场景
4. **性能分析**：能够计算和比较不同算法的性能指标
5. **现代调度**：了解CFS和多核调度的基本原理
6. **问题解决**：掌握优先级反转、饥饿等问题的解决方案
