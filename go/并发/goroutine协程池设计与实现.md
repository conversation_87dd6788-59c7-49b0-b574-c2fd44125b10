# Goroutine协程池设计与实现

## 为什么需要协程池？

### 问题背景
1. **资源消耗**：虽然goroutine很轻量，但大量创建仍会消耗内存和CPU
2. **调度开销**：过多的goroutine会增加调度器的负担
3. **资源控制**：需要限制并发数量，防止系统资源耗尽
4. **任务管理**：需要统一管理和监控goroutine的生命周期

### 协程池的优势
- 复用goroutine，减少创建销毁开销
- 控制并发数量，避免资源耗尽
- 提供任务队列，平滑处理突发流量
- 统一错误处理和监控

## 协程池的基本设计

### 基本设计
```go
type Pool struct {
    capacity  int           // 协程池容量
    running   int32         // 当前运行的goroutine数量
    taskQueue chan func()   // 任务队列
    closed    int32         // 池状态
    wg        sync.WaitGroup
}

func NewPool(capacity int) *Pool {
    p := &Pool{
        capacity:  capacity,
        taskQueue: make(chan func(), capacity*2),
    }

    // 启动worker goroutines
    for i := 0; i < capacity; i++ {
        p.wg.Add(1)
        go p.worker()
    }

    return p
}

func (p *Pool) Submit(task func()) error {
    if atomic.LoadInt32(&p.closed) == 1 {
        return errors.New("pool is closed")
    }

    select {
    case p.taskQueue <- task:
        return nil
    default:
        return errors.New("task queue is full")
    }
}

func (p *Pool) worker() {
    defer p.wg.Done()

    for task := range p.taskQueue {
        if task != nil {
            func() {
                defer func() {
                    if r := recover(); r != nil {
                        log.Printf("Task panic: %v", r)
                    }
                }()
                task()
            }()
        }
    }
}

func (p *Pool) Close() {
    if atomic.CompareAndSwapInt32(&p.closed, 0, 1) {
        close(p.taskQueue)
        p.wg.Wait()
    }
}
```

## 高级特性

### 1. 动态扩缩容
```go
type DynamicPool struct {
    *Pool
    minWorkers int
    maxWorkers int
    metrics    PoolMetrics
}

type PoolMetrics struct {
    TasksSubmitted int64
    TasksCompleted int64
    TasksFailed    int64
}

func (dp *DynamicPool) autoScale() {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()

    for range ticker.C {
        queueLen := len(dp.taskQueue)
        running := atomic.LoadInt32(&dp.running)

        // 扩容：任务积压且未达到最大worker数
        if queueLen > int(running) && running < int32(dp.maxWorkers) {
            dp.addWorker()
        }

        // 缩容：空闲时间过长且超过最小worker数
        if running > int32(dp.minWorkers) {
            dp.removeIdleWorkers()
        }
    }
}
```

### 2. 任务超时控制
```go
func (p *Pool) SubmitWithTimeout(task func(), timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(context.Background(), timeout)
    defer cancel()

    done := make(chan struct{})

    wrappedTask := func() {
        defer close(done)
        task()
    }

    if err := p.Submit(wrappedTask); err != nil {
        return err
    }

    select {
    case <-done:
        return nil
    case <-ctx.Done():
        return errors.New("task timeout")
    }
}
```

## 最佳实践

### 1. 合理设置池大小
```go
func OptimalPoolSize(taskType string) int {
    switch taskType {
    case "cpu":
        return runtime.NumCPU()           // CPU密集型
    case "io":
        return runtime.NumCPU() * 2       // IO密集型
    default:
        return runtime.NumCPU() + 1       // 混合型
    }
}
```

### 2. 错误处理
```go
func (p *Pool) safeExecute(task func()) {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Task panic: %v", r)
        }
    }()
    task()
}
```

### 3. 优雅关闭
```go
func (p *Pool) Shutdown(timeout time.Duration) error {
    atomic.StoreInt32(&p.closed, 1)
    close(p.taskQueue)

    done := make(chan struct{})
    go func() {
        p.wg.Wait()
        close(done)
    }()

    select {
    case <-done:
        return nil
    case <-time.After(timeout):
        return errors.New("shutdown timeout")
    }
}
```

## 高频面试问题

### Q1: 为什么需要协程池？
**问题**：
- 大量goroutine创建销毁开销
- 无限制创建可能导致资源耗尽
- 难以控制并发数量

**解决**：
- 复用goroutine减少开销
- 限制并发数量保护系统
- 统一管理和监控

### Q2: 如何设计协程池？
**核心组件**：
- 任务队列：存储待执行任务
- Worker goroutines：执行任务的工作协程
- 控制机制：启动、停止、监控

**关键设计**：
- 合理设置池大小
- 任务队列缓冲区大小
- 优雅关闭机制
- 错误处理和恢复

### Q3: 协程池大小如何确定？
**考虑因素**：
- 任务类型（CPU密集型 vs IO密集型）
- 系统资源（CPU核心数、内存）
- 业务需求（响应时间、吞吐量）

**经验公式**：
- CPU密集型：核心数
- IO密集型：核心数 × 2
- 混合型：核心数 + 1

### Q4: 如何处理任务panic？
```go
func (p *Pool) safeExecute(task func()) {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Task panic: %v", r)
            // 记录错误，不影响其他任务
        }
    }()
    task()
}
```

### Q5: 协程池的优雅关闭？
**步骤**：
1. 停止接收新任务
2. 等待现有任务完成
3. 设置超时时间
4. 强制关闭超时任务

### Q6: 协程池 vs 直接创建goroutine？
**协程池优势**：
- 控制资源使用
- 减少创建销毁开销
- 便于监控和管理

**直接创建优势**：
- 简单直接
- 无队列等待时间
- 适合短期任务

### Q7: 如何监控协程池状态？
**关键指标**：
- 活跃worker数量
- 任务队列长度
- 任务执行成功/失败率
- 平均等待/执行时间

### 设计要点
1. **任务设计**：避免长时间阻塞任务
2. **错误隔离**：单个任务失败不影响整体
3. **资源控制**：合理设置池大小和队列长度
4. **监控告警**：及时发现性能问题
