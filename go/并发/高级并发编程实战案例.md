# 高级并发编程实战案例

## 1. 并发安全的缓存系统

### 1.1 分片锁缓存实现

```go
package main

import (
    "hash/fnv"
    "sync"
    "time"
)

// 分片锁缓存，减少锁竞争
type ShardedCache struct {
    shards []*CacheShard
    mask   uint32
}

type CacheShard struct {
    mu    sync.RWMutex
    items map[string]*CacheItem
}

type CacheItem struct {
    value      interface{}
    expiration int64
}

func NewShardedCache(shardCount int) *ShardedCache {
    // 确保分片数是2的幂
    if shardCount&(shardCount-1) != 0 {
        panic("分片数必须是2的幂")
    }
    
    shards := make([]*CacheShard, shardCount)
    for i := range shards {
        shards[i] = &CacheShard{
            items: make(map[string]*CacheItem),
        }
    }
    
    return &ShardedCache{
        shards: shards,
        mask:   uint32(shardCount - 1),
    }
}

func (sc *ShardedCache) getShard(key string) *CacheShard {
    hasher := fnv.New32a()
    hasher.Write([]byte(key))
    return sc.shards[hasher.Sum32()&sc.mask]
}

func (sc *ShardedCache) Set(key string, value interface{}, ttl time.Duration) {
    shard := sc.getShard(key)
    shard.mu.Lock()
    defer shard.mu.Unlock()
    
    expiration := int64(0)
    if ttl > 0 {
        expiration = time.Now().Add(ttl).UnixNano()
    }
    
    shard.items[key] = &CacheItem{
        value:      value,
        expiration: expiration,
    }
}

func (sc *ShardedCache) Get(key string) (interface{}, bool) {
    shard := sc.getShard(key)
    shard.mu.RLock()
    defer shard.mu.RUnlock()
    
    item, exists := shard.items[key]
    if !exists {
        return nil, false
    }
    
    // 检查过期
    if item.expiration > 0 && time.Now().UnixNano() > item.expiration {
        delete(shard.items, key)
        return nil, false
    }
    
    return item.value, true
}

// 并发安全的统计信息
func (sc *ShardedCache) Stats() map[string]int {
    stats := map[string]int{
        "total_items": 0,
        "total_shards": len(sc.shards),
    }
    
    for _, shard := range sc.shards {
        shard.mu.RLock()
        stats["total_items"] += len(shard.items)
        shard.mu.RUnlock()
    }
    
    return stats
}

// 缓存性能测试
func benchmarkCache() {
    cache := NewShardedCache(16)
    var wg sync.WaitGroup
    
    // 写入测试
    start := time.Now()
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            for j := 0; j < 10000; j++ {
                key := fmt.Sprintf("key_%d_%d", workerID, j)
                cache.Set(key, j, time.Minute)
            }
        }(i)
    }
    wg.Wait()
    writeTime := time.Since(start)
    
    // 读取测试
    start = time.Now()
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            for j := 0; j < 10000; j++ {
                key := fmt.Sprintf("key_%d_%d", workerID, j)
                cache.Get(key)
            }
        }(i)
    }
    wg.Wait()
    readTime := time.Since(start)
    
    fmt.Printf("写入耗时: %v, 读取耗时: %v\n", writeTime, readTime)
    fmt.Printf("缓存统计: %+v\n", cache.Stats())
}
```

### 1.2 无锁环形缓冲区

```go
import (
    "sync/atomic"
    "unsafe"
)

// 无锁环形缓冲区
type LockFreeRingBuffer struct {
    buffer []unsafe.Pointer
    mask   uint64
    head   uint64 // 写指针
    tail   uint64 // 读指针
}

func NewLockFreeRingBuffer(size int) *LockFreeRingBuffer {
    // 确保大小是2的幂
    if size&(size-1) != 0 {
        panic("大小必须是2的幂")
    }
    
    return &LockFreeRingBuffer{
        buffer: make([]unsafe.Pointer, size),
        mask:   uint64(size - 1),
    }
}

func (rb *LockFreeRingBuffer) Push(item interface{}) bool {
    for {
        head := atomic.LoadUint64(&rb.head)
        tail := atomic.LoadUint64(&rb.tail)
        
        // 检查是否已满
        if head-tail >= uint64(len(rb.buffer)) {
            return false
        }
        
        // 尝试预留位置
        if atomic.CompareAndSwapUint64(&rb.head, head, head+1) {
            // 写入数据
            atomic.StorePointer(&rb.buffer[head&rb.mask], unsafe.Pointer(&item))
            return true
        }
    }
}

func (rb *LockFreeRingBuffer) Pop() (interface{}, bool) {
    for {
        tail := atomic.LoadUint64(&rb.tail)
        head := atomic.LoadUint64(&rb.head)
        
        // 检查是否为空
        if tail >= head {
            return nil, false
        }
        
        // 尝试预留位置
        if atomic.CompareAndSwapUint64(&rb.tail, tail, tail+1) {
            // 读取数据
            ptr := atomic.LoadPointer(&rb.buffer[tail&rb.mask])
            if ptr == nil {
                continue // 数据还未写入，重试
            }
            return *(*interface{})(ptr), true
        }
    }
}

// 生产者消费者测试
func testLockFreeRingBuffer() {
    rb := NewLockFreeRingBuffer(1024)
    var wg sync.WaitGroup
    
    // 生产者
    for i := 0; i < 4; i++ {
        wg.Add(1)
        go func(producerID int) {
            defer wg.Done()
            for j := 0; j < 10000; j++ {
                for !rb.Push(producerID*10000 + j) {
                    runtime.Gosched() // 缓冲区满，让出CPU
                }
            }
        }(i)
    }
    
    // 消费者
    consumed := int64(0)
    for i := 0; i < 2; i++ {
        wg.Add(1)
        go func(consumerID int) {
            defer wg.Done()
            for {
                if item, ok := rb.Pop(); ok {
                    atomic.AddInt64(&consumed, 1)
                    if consumed >= 40000 {
                        return
                    }
                } else {
                    runtime.Gosched()
                }
            }
        }(i)
    }
    
    wg.Wait()
    fmt.Printf("消费了 %d 个项目\n", consumed)
}
```

## 2. 高性能连接池

### 2.1 数据库连接池实现

```go
import (
    "context"
    "database/sql"
    "errors"
    "sync"
    "time"
)

type ConnectionPool struct {
    mu          sync.RWMutex
    connections chan *PooledConnection
    factory     func() (*sql.DB, error)
    maxOpen     int
    maxIdle     int
    maxLifetime time.Duration
    openCount   int
    idleCount   int
    stats       PoolStats
}

type PooledConnection struct {
    conn      *sql.DB
    createdAt time.Time
    lastUsed  time.Time
    inUse     bool
}

type PoolStats struct {
    OpenConnections int
    IdleConnections int
    WaitCount       int64
    WaitDuration    time.Duration
}

func NewConnectionPool(factory func() (*sql.DB, error), maxOpen, maxIdle int, maxLifetime time.Duration) *ConnectionPool {
    pool := &ConnectionPool{
        connections: make(chan *PooledConnection, maxIdle),
        factory:     factory,
        maxOpen:     maxOpen,
        maxIdle:     maxIdle,
        maxLifetime: maxLifetime,
    }
    
    // 启动清理goroutine
    go pool.cleaner()
    
    return pool
}

func (p *ConnectionPool) Get(ctx context.Context) (*PooledConnection, error) {
    waitStart := time.Now()
    
    for {
        select {
        case conn := <-p.connections:
            p.mu.Lock()
            p.idleCount--
            conn.inUse = true
            conn.lastUsed = time.Now()
            p.mu.Unlock()
            
            // 检查连接是否过期
            if p.maxLifetime > 0 && time.Since(conn.createdAt) > p.maxLifetime {
                conn.conn.Close()
                p.mu.Lock()
                p.openCount--
                p.mu.Unlock()
                continue // 重新获取连接
            }
            
            return conn, nil
            
        default:
            // 没有空闲连接，尝试创建新连接
            p.mu.Lock()
            if p.openCount < p.maxOpen {
                p.openCount++
                p.mu.Unlock()
                
                db, err := p.factory()
                if err != nil {
                    p.mu.Lock()
                    p.openCount--
                    p.mu.Unlock()
                    return nil, err
                }
                
                conn := &PooledConnection{
                    conn:      db,
                    createdAt: time.Now(),
                    lastUsed:  time.Now(),
                    inUse:     true,
                }
                
                return conn, nil
            }
            p.mu.Unlock()
            
            // 等待连接可用
            select {
            case <-ctx.Done():
                p.mu.Lock()
                p.stats.WaitCount++
                p.stats.WaitDuration += time.Since(waitStart)
                p.mu.Unlock()
                return nil, ctx.Err()
            case <-time.After(time.Millisecond * 10):
                continue
            }
        }
    }
}

func (p *ConnectionPool) Put(conn *PooledConnection) {
    if conn == nil {
        return
    }
    
    p.mu.Lock()
    defer p.mu.Unlock()
    
    conn.inUse = false
    
    if p.idleCount < p.maxIdle {
        select {
        case p.connections <- conn:
            p.idleCount++
        default:
            // 连接池已满，关闭连接
            conn.conn.Close()
            p.openCount--
        }
    } else {
        // 超过最大空闲连接数，关闭连接
        conn.conn.Close()
        p.openCount--
    }
}

func (p *ConnectionPool) cleaner() {
    ticker := time.NewTicker(time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        p.mu.Lock()
        if p.idleCount == 0 {
            p.mu.Unlock()
            continue
        }
        
        // 清理过期连接
        var toClose []*PooledConnection
        remaining := make([]*PooledConnection, 0, p.idleCount)
        
        // 从channel中取出所有连接进行检查
        for i := 0; i < p.idleCount; i++ {
            select {
            case conn := <-p.connections:
                if p.maxLifetime > 0 && time.Since(conn.createdAt) > p.maxLifetime {
                    toClose = append(toClose, conn)
                } else {
                    remaining = append(remaining, conn)
                }
            default:
                break
            }
        }
        
        // 将未过期的连接放回
        for _, conn := range remaining {
            select {
            case p.connections <- conn:
            default:
                toClose = append(toClose, conn)
            }
        }
        
        p.idleCount = len(remaining)
        p.openCount -= len(toClose)
        p.mu.Unlock()
        
        // 关闭过期连接
        for _, conn := range toClose {
            conn.conn.Close()
        }
    }
}

func (p *ConnectionPool) Stats() PoolStats {
    p.mu.RLock()
    defer p.mu.RUnlock()
    
    stats := p.stats
    stats.OpenConnections = p.openCount
    stats.IdleConnections = p.idleCount
    
    return stats
}
```

## 3. 并发限流器

### 3.1 令牌桶限流器

```go
import (
    "context"
    "sync"
    "time"
)

type TokenBucket struct {
    mu       sync.Mutex
    tokens   float64
    capacity float64
    rate     float64
    lastTime time.Time
}

func NewTokenBucket(capacity, rate float64) *TokenBucket {
    return &TokenBucket{
        tokens:   capacity,
        capacity: capacity,
        rate:     rate,
        lastTime: time.Now(),
    }
}

func (tb *TokenBucket) Allow() bool {
    tb.mu.Lock()
    defer tb.mu.Unlock()
    
    now := time.Now()
    elapsed := now.Sub(tb.lastTime).Seconds()
    
    // 添加新令牌
    tb.tokens = math.Min(tb.capacity, tb.tokens+elapsed*tb.rate)
    tb.lastTime = now
    
    if tb.tokens >= 1 {
        tb.tokens--
        return true
    }
    
    return false
}

func (tb *TokenBucket) AllowN(n int) bool {
    tb.mu.Lock()
    defer tb.mu.Unlock()
    
    now := time.Now()
    elapsed := now.Sub(tb.lastTime).Seconds()
    
    tb.tokens = math.Min(tb.capacity, tb.tokens+elapsed*tb.rate)
    tb.lastTime = now
    
    if tb.tokens >= float64(n) {
        tb.tokens -= float64(n)
        return true
    }
    
    return false
}

// 等待令牌
func (tb *TokenBucket) Wait(ctx context.Context) error {
    for {
        if tb.Allow() {
            return nil
        }
        
        select {
        case <-ctx.Done():
            return ctx.Err()
        case <-time.After(time.Millisecond * 10):
            continue
        }
    }
}

// 滑动窗口限流器
type SlidingWindowLimiter struct {
    mu       sync.RWMutex
    requests []time.Time
    limit    int
    window   time.Duration
}

func NewSlidingWindowLimiter(limit int, window time.Duration) *SlidingWindowLimiter {
    return &SlidingWindowLimiter{
        requests: make([]time.Time, 0),
        limit:    limit,
        window:   window,
    }
}

func (swl *SlidingWindowLimiter) Allow() bool {
    swl.mu.Lock()
    defer swl.mu.Unlock()
    
    now := time.Now()
    cutoff := now.Add(-swl.window)
    
    // 清理过期请求
    validRequests := 0
    for i, reqTime := range swl.requests {
        if reqTime.After(cutoff) {
            swl.requests = swl.requests[i:]
            validRequests = len(swl.requests)
            break
        }
    }
    
    if validRequests == 0 {
        swl.requests = swl.requests[:0]
    }
    
    // 检查是否超过限制
    if len(swl.requests) >= swl.limit {
        return false
    }
    
    // 记录新请求
    swl.requests = append(swl.requests, now)
    return true
}

// 限流器性能测试
func benchmarkLimiters() {
    tokenBucket := NewTokenBucket(100, 10) // 容量100，每秒10个令牌
    slidingWindow := NewSlidingWindowLimiter(100, time.Second)
    
    var wg sync.WaitGroup
    allowed := int64(0)
    denied := int64(0)
    
    // 测试令牌桶
    start := time.Now()
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < 1000; j++ {
                if tokenBucket.Allow() {
                    atomic.AddInt64(&allowed, 1)
                } else {
                    atomic.AddInt64(&denied, 1)
                }
                time.Sleep(time.Microsecond * 100)
            }
        }()
    }
    wg.Wait()
    
    fmt.Printf("令牌桶测试 - 耗时: %v, 允许: %d, 拒绝: %d\n", 
        time.Since(start), allowed, denied)
    
    // 重置计数器
    atomic.StoreInt64(&allowed, 0)
    atomic.StoreInt64(&denied, 0)
    
    // 测试滑动窗口
    start = time.Now()
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < 1000; j++ {
                if slidingWindow.Allow() {
                    atomic.AddInt64(&allowed, 1)
                } else {
                    atomic.AddInt64(&denied, 1)
                }
                time.Sleep(time.Microsecond * 100)
            }
        }()
    }
    wg.Wait()
    
    fmt.Printf("滑动窗口测试 - 耗时: %v, 允许: %d, 拒绝: %d\n", 
        time.Since(start), allowed, denied)
}
```

## 4. 并发任务调度器

### 4.1 优先级任务队列

```go
import (
    "container/heap"
    "context"
    "sync"
    "time"
)

type Task struct {
    ID       string
    Priority int
    Payload  interface{}
    Deadline time.Time
    Retry    int
    MaxRetry int
    Handler  func(interface{}) error
}

type TaskQueue []*Task

func (tq TaskQueue) Len() int { return len(tq) }

func (tq TaskQueue) Less(i, j int) bool {
    // 优先级高的先执行，如果优先级相同则按截止时间排序
    if tq[i].Priority != tq[j].Priority {
        return tq[i].Priority > tq[j].Priority
    }
    return tq[i].Deadline.Before(tq[j].Deadline)
}

func (tq TaskQueue) Swap(i, j int) {
    tq[i], tq[j] = tq[j], tq[i]
}

func (tq *TaskQueue) Push(x interface{}) {
    *tq = append(*tq, x.(*Task))
}

func (tq *TaskQueue) Pop() interface{} {
    old := *tq
    n := len(old)
    task := old[n-1]
    *tq = old[0 : n-1]
    return task
}

type TaskScheduler struct {
    mu          sync.Mutex
    queue       *TaskQueue
    workers     int
    running     bool
    ctx         context.Context
    cancel      context.CancelFunc
    wg          sync.WaitGroup
    taskChan    chan struct{}
    stats       SchedulerStats
}

type SchedulerStats struct {
    TotalTasks     int64
    CompletedTasks int64
    FailedTasks    int64
    RetryTasks     int64
}

func NewTaskScheduler(workers int) *TaskScheduler {
    ctx, cancel := context.WithCancel(context.Background())
    queue := &TaskQueue{}
    heap.Init(queue)
    
    return &TaskScheduler{
        queue:    queue,
        workers:  workers,
        ctx:      ctx,
        cancel:   cancel,
        taskChan: make(chan struct{}, 1),
    }
}

func (ts *TaskScheduler) Start() {
    ts.mu.Lock()
    if ts.running {
        ts.mu.Unlock()
        return
    }
    ts.running = true
    ts.mu.Unlock()
    
    // 启动工作协程
    for i := 0; i < ts.workers; i++ {
        ts.wg.Add(1)
        go ts.worker(i)
    }
}

func (ts *TaskScheduler) Stop() {
    ts.mu.Lock()
    if !ts.running {
        ts.mu.Unlock()
        return
    }
    ts.running = false
    ts.mu.Unlock()
    
    ts.cancel()
    ts.wg.Wait()
}

func (ts *TaskScheduler) Submit(task *Task) {
    ts.mu.Lock()
    defer ts.mu.Unlock()
    
    heap.Push(ts.queue, task)
    ts.stats.TotalTasks++
    
    // 通知有新任务
    select {
    case ts.taskChan <- struct{}{}:
    default:
    }
}

func (ts *TaskScheduler) worker(id int) {
    defer ts.wg.Done()
    
    for {
        select {
        case <-ts.ctx.Done():
            return
        case <-ts.taskChan:
            ts.processTask(id)
        }
    }
}

func (ts *TaskScheduler) processTask(workerID int) {
    for {
        ts.mu.Lock()
        if ts.queue.Len() == 0 {
            ts.mu.Unlock()
            return
        }
        
        task := heap.Pop(ts.queue).(*Task)
        ts.mu.Unlock()
        
        // 检查任务是否过期
        if !task.Deadline.IsZero() && time.Now().After(task.Deadline) {
            fmt.Printf("Worker %d: 任务 %s 已过期\n", workerID, task.ID)
            ts.mu.Lock()
            ts.stats.FailedTasks++
            ts.mu.Unlock()
            continue
        }
        
        // 执行任务
        fmt.Printf("Worker %d: 执行任务 %s (优先级: %d)\n", 
            workerID, task.ID, task.Priority)
        
        err := task.Handler(task.Payload)
        
        ts.mu.Lock()
        if err != nil {
            if task.Retry < task.MaxRetry {
                task.Retry++
                heap.Push(ts.queue, task)
                ts.stats.RetryTasks++
                fmt.Printf("Worker %d: 任务 %s 重试 (%d/%d)\n", 
                    workerID, task.ID, task.Retry, task.MaxRetry)
            } else {
                ts.stats.FailedTasks++
                fmt.Printf("Worker %d: 任务 %s 失败\n", workerID, task.ID)
            }
        } else {
            ts.stats.CompletedTasks++
            fmt.Printf("Worker %d: 任务 %s 完成\n", workerID, task.ID)
        }
        ts.mu.Unlock()
    }
}

func (ts *TaskScheduler) Stats() SchedulerStats {
    ts.mu.Lock()
    defer ts.mu.Unlock()
    return ts.stats
}

// 任务调度器测试
func testTaskScheduler() {
    scheduler := NewTaskScheduler(4)
    scheduler.Start()
    defer scheduler.Stop()
    
    // 提交不同优先级的任务
    tasks := []*Task{
        {
            ID:       "high-1",
            Priority: 10,
            Payload:  "高优先级任务1",
            Deadline: time.Now().Add(time.Second * 5),
            MaxRetry: 3,
            Handler: func(payload interface{}) error {
                time.Sleep(time.Millisecond * 100)
                return nil
            },
        },
        {
            ID:       "low-1",
            Priority: 1,
            Payload:  "低优先级任务1",
            Deadline: time.Now().Add(time.Second * 10),
            MaxRetry: 2,
            Handler: func(payload interface{}) error {
                time.Sleep(time.Millisecond * 200)
                return nil
            },
        },
        {
            ID:       "medium-1",
            Priority: 5,
            Payload:  "中优先级任务1",
            Deadline: time.Now().Add(time.Second * 3),
            MaxRetry: 1,
            Handler: func(payload interface{}) error {
                time.Sleep(time.Millisecond * 150)
                // 模拟失败
                if time.Now().UnixNano()%2 == 0 {
                    return fmt.Errorf("随机失败")
                }
                return nil
            },
        },
    }
    
    for _, task := range tasks {
        scheduler.Submit(task)
    }
    
    // 等待任务完成
    time.Sleep(time.Second * 2)
    
    stats := scheduler.Stats()
    fmt.Printf("调度器统计: %+v\n", stats)
}
```

## 5. 面试重点问题

### Q1: 如何设计一个高性能的并发缓存？
- **分片锁**：减少锁竞争
- **读写分离**：使用RWMutex
- **过期清理**：后台goroutine定期清理
- **内存管理**：LRU淘汰策略

### Q2: 无锁编程的优势和挑战？
- **优势**：避免锁竞争，提高性能
- **挑战**：ABA问题，内存序问题，复杂性高
- **适用场景**：高频操作，简单数据结构

### Q3: 如何实现一个公平的限流器？
- **令牌桶**：平滑限流，允许突发
- **滑动窗口**：精确控制，内存开销大
- **漏桶算法**：严格限流，平滑输出

### Q4: 并发任务调度的关键考虑？
- **优先级调度**：使用堆数据结构
- **负载均衡**：工作窃取算法
- **故障处理**：重试机制和熔断
- **监控统计**：性能指标收集

这些高级并发编程模式是构建高性能系统的核心技术，掌握它们对于系统架构设计至关重要。
