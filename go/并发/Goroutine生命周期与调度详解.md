# Goroutine生命周期与调度详解

## Goroutine生命周期

### 1. Goroutine状态转换

```go
// Goroutine状态枚举
const (
    _Gidle = iota // 0: 刚刚被分配，还没有初始化
    _Grunnable    // 1: 在运行队列中，等待被调度
    _Grunning     // 2: 正在执行Go代码
    _Gsyscall     // 3: 正在执行系统调用
    _Gwaiting     // 4: 被阻塞（等待某个条件）
    _Gdead        // 6: 刚刚退出或正在被初始化
    _Gcopystack   // 8: 栈正在被复制
    _Gpreempted   // 9: 被抢占，等待重新调度
)
```

### 2. 状态转换图

```
创建 -> _Gidle -> _Grunnable -> _Grunning -> _Gdead
                      ↑             ↓
                      ←─ _Gwaiting ←─
                      ↑             ↓
                      ←─ _Gsyscall ←─
                      ↑             ↓
                      ←─ _Gpreempted ←─
```

### 3. 生命周期示例代码

```go
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

// 演示Goroutine生命周期
func demonstrateGoroutineLifecycle() {
    var wg sync.WaitGroup
    
    // 1. 创建阶段 (_Gidle -> _Grunnable)
    fmt.Println("创建Goroutine...")
    wg.Add(3)
    
    // 2. CPU密集型任务 - 展示抢占调度
    go func() {
        defer wg.Done()
        fmt.Printf("CPU密集型任务开始，Goroutine ID: %d\n", getGoroutineID())
        
        // 模拟CPU密集型计算
        sum := 0
        for i := 0; i < 1000000; i++ {
            sum += i
            // 每10万次迭代检查一次调度
            if i%100000 == 0 {
                runtime.Gosched() // 主动让出CPU
            }
        }
        fmt.Printf("CPU密集型任务完成，结果: %d\n", sum)
    }()
    
    // 3. I/O阻塞任务 - 展示阻塞状态
    go func() {
        defer wg.Done()
        fmt.Printf("I/O阻塞任务开始，Goroutine ID: %d\n", getGoroutineID())
        
        // 模拟I/O阻塞 (_Grunning -> _Gwaiting)
        time.Sleep(100 * time.Millisecond)
        
        fmt.Println("I/O阻塞任务完成")
    }()
    
    // 4. 系统调用任务 - 展示系统调用状态
    go func() {
        defer wg.Done()
        fmt.Printf("系统调用任务开始，Goroutine ID: %d\n", getGoroutineID())
        
        // 模拟系统调用 (_Grunning -> _Gsyscall)
        runtime.GC() // 触发垃圾回收
        
        fmt.Println("系统调用任务完成")
    }()
    
    wg.Wait()
    fmt.Println("所有Goroutine执行完成 (_Gdead)")
}

// 获取当前Goroutine ID（仅用于演示）
func getGoroutineID() int {
    var buf [64]byte
    n := runtime.Stack(buf[:], false)
    // 简化的ID提取，实际应用中不推荐
    return int(buf[0]) % 1000
}
```

## Goroutine调度详解

### 1. GMP调度模型深入

```go
// GMP模型组件详解
type G struct {
    // Goroutine栈信息
    stack       stack   // 栈边界
    stackguard0 uintptr // 栈溢出检查
    
    // 调度信息
    sched     gobuf   // 调度上下文
    goid      int64   // Goroutine ID
    gopc      uintptr // 创建该goroutine的PC
    
    // 状态信息
    atomicstatus uint32 // Goroutine状态
    preempt      bool   // 抢占标志
    preemptStop  bool   // 抢占停止标志
}

type M struct {
    g0      *g     // 调度goroutine
    curg    *g     // 当前运行的goroutine
    p       puintptr // 绑定的P
    nextp   puintptr // 下一个要绑定的P
    oldp    puintptr // 执行系统调用前绑定的P
    
    spinning bool   // M是否在寻找工作
    blocked  bool   // M是否被阻塞
    
    thread   uintptr // 线程句柄
    freelink *m      // 空闲M链表
}

type P struct {
    id          int32
    status      uint32 // P的状态
    link        puintptr
    schedtick   uint32 // 调度计数
    syscalltick uint32 // 系统调用计数
    
    // 本地运行队列
    runqhead uint32
    runqtail uint32
    runq     [256]guintptr
    
    // 空闲G列表
    gFree struct {
        gList
        n int32
    }
}
```

### 2. 调度时机

```go
// 调度发生的时机
func demonstrateSchedulingPoints() {
    fmt.Println("=== Goroutine调度时机演示 ===")
    
    // 1. 主动调度 - runtime.Gosched()
    go func() {
        fmt.Println("1. 主动调度示例")
        for i := 0; i < 3; i++ {
            fmt.Printf("  执行任务 %d\n", i)
            runtime.Gosched() // 主动让出CPU
        }
    }()
    
    // 2. 阻塞调度 - channel操作
    ch := make(chan int)
    go func() {
        fmt.Println("2. 阻塞调度示例")
        <-ch // 阻塞等待，触发调度
        fmt.Println("  接收到数据，继续执行")
    }()
    
    // 3. 系统调用调度
    go func() {
        fmt.Println("3. 系统调用调度示例")
        time.Sleep(10 * time.Millisecond) // 系统调用，触发调度
        fmt.Println("  系统调用完成")
    }()
    
    // 4. 抢占调度（Go 1.14+）
    go func() {
        fmt.Println("4. 抢占调度示例")
        sum := 0
        for i := 0; i < 1000000; i++ {
            sum += i
            // 长时间运行的循环可能被抢占
        }
        fmt.Printf("  计算完成: %d\n", sum)
    }()
    
    time.Sleep(50 * time.Millisecond)
    ch <- 1
    time.Sleep(50 * time.Millisecond)
}
```

### 3. 工作窃取算法

```go
// 工作窃取演示
func demonstrateWorkStealing() {
    fmt.Println("=== 工作窃取算法演示 ===")
    
    numCPU := runtime.NumCPU()
    fmt.Printf("CPU核心数: %d\n", numCPU)
    
    // 创建大量短任务
    const numTasks = 1000
    var wg sync.WaitGroup
    wg.Add(numTasks)
    
    start := time.Now()
    
    for i := 0; i < numTasks; i++ {
        go func(taskID int) {
            defer wg.Done()
            
            // 模拟短任务
            sum := 0
            for j := 0; j < 1000; j++ {
                sum += j
            }
            
            if taskID%100 == 0 {
                fmt.Printf("任务 %d 完成，P: %d\n", taskID, runtime.GOMAXPROCS(0))
            }
        }(i)
    }
    
    wg.Wait()
    duration := time.Since(start)
    fmt.Printf("完成 %d 个任务，耗时: %v\n", numTasks, duration)
}
```

## 面试常见问题

### 1. Goroutine vs 线程

```go
// Goroutine与线程对比
func compareGoroutineAndThread() {
    fmt.Println("=== Goroutine vs 线程对比 ===")
    
    // 内存占用对比
    fmt.Println("1. 内存占用:")
    fmt.Println("   - Goroutine: 2KB初始栈，可动态增长")
    fmt.Println("   - 线程: 通常8MB固定栈")
    
    // 创建开销对比
    start := time.Now()
    var wg sync.WaitGroup
    
    const numGoroutines = 10000
    wg.Add(numGoroutines)
    
    for i := 0; i < numGoroutines; i++ {
        go func() {
            defer wg.Done()
            // 简单操作
            _ = 1 + 1
        }()
    }
    
    wg.Wait()
    duration := time.Since(start)
    fmt.Printf("2. 创建开销: 创建%d个Goroutine耗时: %v\n", numGoroutines, duration)
    
    // 调度开销
    fmt.Println("3. 调度开销:")
    fmt.Println("   - Goroutine: 用户态调度，开销小")
    fmt.Println("   - 线程: 内核态调度，开销大")
}
```

### 2. Goroutine泄漏检测

```go
// Goroutine泄漏示例和检测
func demonstrateGoroutineLeak() {
    fmt.Println("=== Goroutine泄漏检测 ===")
    
    // 记录初始Goroutine数量
    initialCount := runtime.NumGoroutine()
    fmt.Printf("初始Goroutine数量: %d\n", initialCount)
    
    // 创建会泄漏的Goroutine
    createLeakyGoroutines()
    
    // 强制GC
    runtime.GC()
    time.Sleep(100 * time.Millisecond)
    
    // 检查泄漏
    currentCount := runtime.NumGoroutine()
    fmt.Printf("当前Goroutine数量: %d\n", currentCount)
    
    if currentCount > initialCount {
        fmt.Printf("检测到Goroutine泄漏: %d个\n", currentCount-initialCount)
        
        // 打印Goroutine堆栈
        buf := make([]byte, 1<<16)
        stackSize := runtime.Stack(buf, true)
        fmt.Printf("Goroutine堆栈:\n%s\n", buf[:stackSize])
    }
}

func createLeakyGoroutines() {
    // 泄漏示例1: 无缓冲channel永久阻塞
    ch := make(chan int)
    go func() {
        <-ch // 永远不会收到数据，造成泄漏
    }()
    
    // 泄漏示例2: 无限循环
    go func() {
        for {
            select {
            case <-time.After(time.Hour): // 长时间等待
                return
            default:
                // 继续循环
            }
        }
    }()
}
```

## 最佳实践

### 1. Goroutine池

```go
// 简单的Goroutine池实现
type GoroutinePool struct {
    workers    int
    taskQueue  chan func()
    wg         sync.WaitGroup
    quit       chan bool
}

func NewGoroutinePool(workers int, queueSize int) *GoroutinePool {
    pool := &GoroutinePool{
        workers:   workers,
        taskQueue: make(chan func(), queueSize),
        quit:      make(chan bool),
    }
    
    pool.start()
    return pool
}

func (p *GoroutinePool) start() {
    for i := 0; i < p.workers; i++ {
        p.wg.Add(1)
        go p.worker()
    }
}

func (p *GoroutinePool) worker() {
    defer p.wg.Done()
    
    for {
        select {
        case task := <-p.taskQueue:
            task()
        case <-p.quit:
            return
        }
    }
}

func (p *GoroutinePool) Submit(task func()) {
    select {
    case p.taskQueue <- task:
    default:
        // 队列满，可以选择阻塞或丢弃
        fmt.Println("任务队列已满")
    }
}

func (p *GoroutinePool) Stop() {
    close(p.quit)
    p.wg.Wait()
}
```

### 2. 优雅关闭

```go
// 优雅关闭示例
func demonstrateGracefulShutdown() {
    ctx, cancel := context.WithCancel(context.Background())
    var wg sync.WaitGroup
    
    // 启动工作Goroutine
    for i := 0; i < 3; i++ {
        wg.Add(1)
        go worker(ctx, &wg, i)
    }
    
    // 模拟运行一段时间
    time.Sleep(2 * time.Second)
    
    // 发送关闭信号
    fmt.Println("发送关闭信号...")
    cancel()
    
    // 等待所有Goroutine完成
    wg.Wait()
    fmt.Println("所有Goroutine已优雅关闭")
}

func worker(ctx context.Context, wg *sync.WaitGroup, id int) {
    defer wg.Done()
    
    ticker := time.NewTicker(500 * time.Millisecond)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            fmt.Printf("Worker %d 收到关闭信号，正在清理...\n", id)
            // 执行清理工作
            time.Sleep(100 * time.Millisecond)
            fmt.Printf("Worker %d 清理完成\n", id)
            return
        case <-ticker.C:
            fmt.Printf("Worker %d 正在工作...\n", id)
        }
    }
}

func main() {
    demonstrateGoroutineLifecycle()
    fmt.Println()
    demonstrateSchedulingPoints()
    fmt.Println()
    demonstrateWorkStealing()
    fmt.Println()
    compareGoroutineAndThread()
    fmt.Println()
    demonstrateGoroutineLeak()
    fmt.Println()
    demonstrateGracefulShutdown()
}
```

## 面试要点总结

1. **生命周期理解**: 掌握Goroutine的各种状态及转换条件
2. **调度机制**: 理解GMP模型和工作窃取算法
3. **性能优势**: 了解Goroutine相比线程的优势
4. **泄漏检测**: 能够识别和避免Goroutine泄漏
5. **最佳实践**: 掌握Goroutine池、优雅关闭等模式
6. **调试技能**: 能够使用runtime包进行调试和监控
