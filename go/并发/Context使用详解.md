# Context使用详解

## Context概述

Context是Go语言中用于管理goroutine生命周期的重要工具，主要用于：
- 取消信号传递
- 超时控制
- 截止时间管理
- 请求范围数据传递

## Context类型

### 1. 基础Context
```go
// 根Context，永不取消
ctx := context.Background()

// 空Context，用于测试
ctx := context.TODO()
```

### 2. 可取消Context
```go
// 手动取消
ctx, cancel := context.WithCancel(context.Background())
defer cancel() // 确保资源释放

// 超时取消
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

// 截止时间取消
deadline := time.Now().Add(10 * time.Second)
ctx, cancel := context.WithDeadline(context.Background(), deadline)
defer cancel()
```

### 3. 携带数据的Context
```go
// 传递请求ID
ctx := context.WithValue(context.Background(), "requestID", "12345")

// 获取数据
if requestID := ctx.Value("requestID"); requestID != nil {
    fmt.Println("Request ID:", requestID)
}
```

## 使用场景

### 1. HTTP请求超时控制
```go
func httpRequestWithTimeout(url string, timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(context.Background(), timeout)
    defer cancel()
    
    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return err
    }
    
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    return nil
}
```

### 2. 数据库操作超时
```go
func queryWithTimeout(db *sql.DB, query string, timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(context.Background(), timeout)
    defer cancel()
    
    rows, err := db.QueryContext(ctx, query)
    if err != nil {
        return err
    }
    defer rows.Close()
    
    // 处理结果...
    return nil
}
```

### 3. Goroutine协调
```go
func workerWithContext(ctx context.Context, id int) {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            fmt.Printf("Worker %d stopped: %v\n", id, ctx.Err())
            return
        case <-ticker.C:
            fmt.Printf("Worker %d working...\n", id)
        }
    }
}

func main() {
    ctx, cancel := context.WithCancel(context.Background())
    
    // 启动多个worker
    for i := 0; i < 3; i++ {
        go workerWithContext(ctx, i)
    }
    
    time.Sleep(5 * time.Second)
    cancel() // 取消所有worker
    time.Sleep(time.Second)
}
```

### 4. 管道处理
```go
func pipeline(ctx context.Context) <-chan int {
    out := make(chan int)
    
    go func() {
        defer close(out)
        for i := 0; i < 10; i++ {
            select {
            case out <- i:
            case <-ctx.Done():
                return
            }
            time.Sleep(100 * time.Millisecond)
        }
    }()
    
    return out
}

func main() {
    ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
    defer cancel()
    
    for num := range pipeline(ctx) {
        fmt.Println(num)
    }
}
```

## 最佳实践

### 1. Context传递规则
- Context应该作为函数的第一个参数
- 不要将Context存储在结构体中
- 不要传递nil Context，使用context.TODO()

```go
// 正确的函数签名
func DoSomething(ctx context.Context, arg string) error {
    // 实现...
    return nil
}

// 错误的设计
type Service struct {
    ctx context.Context // 不要这样做
}
```

### 2. 超时设置
```go
func processWithTimeout(data string) error {
    // 为每个操作设置合理的超时时间
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    return processData(ctx, data)
}
```

### 3. 错误处理
```go
func handleContextError(ctx context.Context) error {
    select {
    case <-ctx.Done():
        switch ctx.Err() {
        case context.Canceled:
            return errors.New("operation was canceled")
        case context.DeadlineExceeded:
            return errors.New("operation timed out")
        default:
            return ctx.Err()
        }
    default:
        return nil
    }
}
```

### 4. 值传递注意事项
```go
// 定义类型化的key，避免冲突
type contextKey string

const (
    UserIDKey    contextKey = "userID"
    RequestIDKey contextKey = "requestID"
)

func setUserID(ctx context.Context, userID string) context.Context {
    return context.WithValue(ctx, UserIDKey, userID)
}

func getUserID(ctx context.Context) (string, bool) {
    userID, ok := ctx.Value(UserIDKey).(string)
    return userID, ok
}
```

## 高频面试问题

### Q1: Context的作用是什么？
**主要功能**：
- 取消信号传递：协调多个goroutine的退出
- 超时控制：防止操作无限期阻塞
- 截止时间管理：设置操作的最后期限
- 数据传递：在调用链中传递请求范围的数据

### Q2: Context.WithCancel vs WithTimeout的区别？
**WithCancel**：
- 手动控制取消时机
- 适合需要主动取消的场景
- 必须手动调用cancel()

**WithTimeout**：
- 自动超时取消
- 适合有时间限制的操作
- 超时后自动取消，但仍建议手动cancel()

### Q3: Context传递的最佳实践？
**规则**：
1. Context作为函数第一个参数
2. 不要存储在结构体中
3. 不要传递nil Context
4. 使用类型化的key传递值
5. 及时调用cancel()释放资源

### Q4: Context.Value的使用场景？
**适用场景**：
- 请求ID、用户ID等请求范围数据
- 认证信息、权限信息
- 链路追踪信息

**注意事项**：
- 不要传递可选参数
- 不要传递业务逻辑数据
- 使用类型化的key避免冲突

### Q5: 如何避免Context泄漏？
**防范措施**：
1. 总是调用cancel()函数
2. 使用defer确保cancel()被调用
3. 不要忽略Context.Done()信号
4. 合理设置超时时间

### Q6: Context在微服务中的应用？
**应用场景**：
- HTTP请求超时控制
- 数据库操作超时
- 服务间调用超时
- 请求链路追踪
- 优雅关闭

### 核心原则
1. **及时响应取消信号**：定期检查ctx.Done()
2. **合理设置超时**：根据业务需求设置超时时间
3. **正确传递Context**：遵循传递规则和最佳实践
4. **避免滥用Value**：只传递请求范围的必要数据
