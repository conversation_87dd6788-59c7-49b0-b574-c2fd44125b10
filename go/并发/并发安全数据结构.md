# 并发安全数据结构

## 概述

并发安全数据结构是指在多个goroutine同时访问时能保证数据一致性和正确性的数据结构。Go语言提供了多种并发安全的数据结构和同步原语。

## 内置并发安全数据结构

### 1. sync.Map
适用于读多写少的场景，内部使用分离的读写map优化性能。

```go
var m sync.Map

// 存储
m.Store("key1", "value1")
m.Store("key2", "value2")

// 读取
if value, ok := m.Load("key1"); ok {
    fmt.Println("key1:", value)
}

// 读取或存储
actual, loaded := m.LoadOrStore("key3", "value3")
fmt.Println("actual:", actual, "loaded:", loaded)

// 删除
m.Delete("key1")

// 遍历
m.Range(func(key, value interface{}) bool {
    fmt.Printf("%v: %v\n", key, value)
    return true // 继续遍历
})
```

### 2. sync.Pool
对象池，用于复用对象减少GC压力。

```go
var pool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024) // 创建新对象
    },
}

// 获取对象
buf := pool.Get().([]byte)

// 使用对象
// ... 使用buf ...

// 归还对象
pool.Put(buf)
```

### 3. Channel
Go语言的核心并发原语，天然并发安全。

```go
// 缓冲channel作为并发安全队列
queue := make(chan int, 100)

// 生产者
go func() {
    for i := 0; i < 10; i++ {
        queue <- i
    }
    close(queue)
}()

// 消费者
for item := range queue {
    fmt.Println("处理:", item)
}
```

## 自定义并发安全数据结构

### 1. 并发安全计数器
```go
type SafeCounter struct {
    mu    sync.RWMutex
    count int64
}

func (c *SafeCounter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.count++
}

func (c *SafeCounter) Decrement() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.count--
}

func (c *SafeCounter) Value() int64 {
    c.mu.RLock()
    defer c.mu.RUnlock()
    return c.count
}

// 使用原子操作的版本
type AtomicCounter struct {
    count int64
}

func (c *AtomicCounter) Increment() {
    atomic.AddInt64(&c.count, 1)
}

func (c *AtomicCounter) Decrement() {
    atomic.AddInt64(&c.count, -1)
}

func (c *AtomicCounter) Value() int64 {
    return atomic.LoadInt64(&c.count)
}
```

### 2. 并发安全队列
```go
type SafeQueue struct {
    mu    sync.Mutex
    items []interface{}
}

func NewSafeQueue() *SafeQueue {
    return &SafeQueue{
        items: make([]interface{}, 0),
    }
}

func (q *SafeQueue) Enqueue(item interface{}) {
    q.mu.Lock()
    defer q.mu.Unlock()
    q.items = append(q.items, item)
}

func (q *SafeQueue) Dequeue() (interface{}, bool) {
    q.mu.Lock()
    defer q.mu.Unlock()
    
    if len(q.items) == 0 {
        return nil, false
    }
    
    item := q.items[0]
    q.items = q.items[1:]
    return item, true
}

func (q *SafeQueue) Size() int {
    q.mu.Lock()
    defer q.mu.Unlock()
    return len(q.items)
}
```

### 3. 并发安全Set
```go
type SafeSet struct {
    mu   sync.RWMutex
    data map[interface{}]struct{}
}

func NewSafeSet() *SafeSet {
    return &SafeSet{
        data: make(map[interface{}]struct{}),
    }
}

func (s *SafeSet) Add(item interface{}) {
    s.mu.Lock()
    defer s.mu.Unlock()
    s.data[item] = struct{}{}
}

func (s *SafeSet) Remove(item interface{}) {
    s.mu.Lock()
    defer s.mu.Unlock()
    delete(s.data, item)
}

func (s *SafeSet) Contains(item interface{}) bool {
    s.mu.RLock()
    defer s.mu.RUnlock()
    _, exists := s.data[item]
    return exists
}

func (s *SafeSet) Size() int {
    s.mu.RLock()
    defer s.mu.RUnlock()
    return len(s.data)
}

func (s *SafeSet) ToSlice() []interface{} {
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    result := make([]interface{}, 0, len(s.data))
    for item := range s.data {
        result = append(result, item)
    }
    return result
}
```

### 4. 并发安全LRU缓存
```go
type LRUCache struct {
    mu       sync.Mutex
    capacity int
    cache    map[string]*Node
    head     *Node
    tail     *Node
}

type Node struct {
    key   string
    value interface{}
    prev  *Node
    next  *Node
}

func NewLRUCache(capacity int) *LRUCache {
    head := &Node{}
    tail := &Node{}
    head.next = tail
    tail.prev = head
    
    return &LRUCache{
        capacity: capacity,
        cache:    make(map[string]*Node),
        head:     head,
        tail:     tail,
    }
}

func (c *LRUCache) Get(key string) (interface{}, bool) {
    c.mu.Lock()
    defer c.mu.Unlock()
    
    if node, exists := c.cache[key]; exists {
        c.moveToHead(node)
        return node.value, true
    }
    return nil, false
}

func (c *LRUCache) Put(key string, value interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()
    
    if node, exists := c.cache[key]; exists {
        node.value = value
        c.moveToHead(node)
        return
    }
    
    newNode := &Node{key: key, value: value}
    c.cache[key] = newNode
    c.addToHead(newNode)
    
    if len(c.cache) > c.capacity {
        tail := c.removeTail()
        delete(c.cache, tail.key)
    }
}

func (c *LRUCache) moveToHead(node *Node) {
    c.removeNode(node)
    c.addToHead(node)
}

func (c *LRUCache) removeNode(node *Node) {
    node.prev.next = node.next
    node.next.prev = node.prev
}

func (c *LRUCache) addToHead(node *Node) {
    node.prev = c.head
    node.next = c.head.next
    c.head.next.prev = node
    c.head.next = node
}

func (c *LRUCache) removeTail() *Node {
    lastNode := c.tail.prev
    c.removeNode(lastNode)
    return lastNode
}
```

## 性能对比

### 不同同步方式的性能特点

1. **Mutex**：
   - 适用场景：保护临界区，读写操作都需要互斥
   - 性能：中等，有锁竞争开销

2. **RWMutex**：
   - 适用场景：读多写少的场景
   - 性能：读操作并发性能好，写操作性能与Mutex类似

3. **原子操作**：
   - 适用场景：简单数值操作
   - 性能：最高，无锁操作

4. **Channel**：
   - 适用场景：数据传递，goroutine间通信
   - 性能：中等，有调度开销

5. **sync.Map**：
   - 适用场景：读多写少的map操作
   - 性能：读操作性能优秀，写操作性能一般

## 高频面试问题

### Q1: sync.Map vs map+mutex的区别？
**sync.Map优势**：
- 读多写少场景性能更好
- 内部使用读写分离优化
- 无需手动加锁

**map+mutex优势**：
- 简单直接，易于理解
- 适合读写比例相近的场景
- 可以批量操作

### Q2: 什么时候使用原子操作？
**适用场景**：
- 简单数值操作（计数器、标志位）
- 指针操作（配置更新）
- 性能要求极高的场景

**不适用场景**：
- 复杂逻辑操作
- 多个变量需要原子性
- 需要条件判断的操作

### Q3: 如何选择合适的同步原语？
**选择原则**：
1. **简单数值操作**：原子操作
2. **保护临界区**：Mutex
3. **读多写少**：RWMutex或sync.Map
4. **数据传递**：Channel
5. **对象复用**：sync.Pool

### Q4: 并发安全数据结构的设计原则？
**设计原则**：
1. **最小锁粒度**：减少锁的范围
2. **读写分离**：读操作使用读锁
3. **避免死锁**：统一加锁顺序
4. **性能优化**：使用原子操作优化热点
5. **接口设计**：提供简洁易用的API

### Q5: 如何避免并发安全问题？
**防范措施**：
1. **使用合适的同步原语**
2. **遵循单一职责原则**
3. **避免共享可变状态**
4. **使用不可变数据结构**
5. **充分测试**：使用race detector

### 最佳实践
1. **优先使用内置的并发安全数据结构**
2. **根据读写比例选择合适的同步方式**
3. **避免过度设计，保持简单**
4. **注意性能测试和优化**
5. **使用工具检测竞态条件**
