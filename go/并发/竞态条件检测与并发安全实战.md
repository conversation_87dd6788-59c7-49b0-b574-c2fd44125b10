# 竞态条件检测与并发安全实战

## 1. 竞态条件基础概念

### 1.1 什么是竞态条件

竞态条件（Race Condition）是指程序的执行结果依赖于不同线程或goroutine的执行时序，当多个goroutine同时访问共享资源时，如果没有适当的同步机制，就可能产生不确定的结果。

```go
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

// 典型的竞态条件示例
var counter int

func raceConditionExample() {
    var wg sync.WaitGroup
    
    // 启动100个goroutine同时修改counter
    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < 1000; j++ {
                counter++ // 竞态条件！
            }
        }()
    }
    
    wg.Wait()
    fmt.Printf("期望结果: 100000, 实际结果: %d\n", counter)
    // 实际结果通常小于100000，每次运行结果都不同
}
```

### 1.2 竞态条件的危害

```go
// 银行账户转账中的竞态条件
type BankAccount struct {
    balance int64
    mu      sync.Mutex // 正确的做法应该使用锁
}

// 错误的实现：存在竞态条件
func (ba *BankAccount) WithdrawUnsafe(amount int64) bool {
    if ba.balance >= amount {
        // 在这里可能被其他goroutine中断
        time.Sleep(time.Microsecond) // 模拟处理时间
        ba.balance -= amount
        return true
    }
    return false
}

// 正确的实现：使用互斥锁
func (ba *BankAccount) WithdrawSafe(amount int64) bool {
    ba.mu.Lock()
    defer ba.mu.Unlock()
    
    if ba.balance >= amount {
        ba.balance -= amount
        return true
    }
    return false
}

// 演示竞态条件的危害
func demonstrateBankingRace() {
    account := &BankAccount{balance: 1000}
    var wg sync.WaitGroup
    
    // 10个goroutine同时尝试取款100元
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            if account.WithdrawUnsafe(100) {
                fmt.Printf("Goroutine %d: 取款成功\n", id)
            } else {
                fmt.Printf("Goroutine %d: 余额不足\n", id)
            }
        }(i)
    }
    
    wg.Wait()
    fmt.Printf("最终余额: %d (应该是0或正数)\n", account.balance)
    // 可能出现负数余额！
}
```

## 2. Go竞态检测器

### 2.1 使用竞态检测器

```bash
# 编译时启用竞态检测
go build -race -o myprogram main.go

# 运行时启用竞态检测
go run -race main.go

# 测试时启用竞态检测
go test -race ./...

# 设置环境变量控制竞态检测器行为
GORACE="log_path=./race_log halt_on_error=1" go run -race main.go
```

### 2.2 竞态检测器报告解读

```go
// 这段代码会触发竞态检测器报告
func triggerRaceDetector() {
    var data int
    var wg sync.WaitGroup
    
    wg.Add(2)
    
    // 写goroutine
    go func() {
        defer wg.Done()
        data = 1 // 写操作
    }()
    
    // 读goroutine
    go func() {
        defer wg.Done()
        fmt.Println(data) // 读操作
    }()
    
    wg.Wait()
}

/*
竞态检测器输出示例：
==================
WARNING: DATA RACE
Write at 0x00c000014088 by goroutine 7:
  main.triggerRaceDetector.func1()
      /path/to/file.go:XX +0x3c

Previous read at 0x00c000014088 by goroutine 8:
  main.triggerRaceDetector.func2()
      /path/to/file.go:XX +0x3c

Goroutine 7 (running) created at:
  main.triggerRaceDetector()
      /path/to/file.go:XX +0x7c

Goroutine 8 (running) created at:
  main.triggerRaceDetector()
      /path/to/file.go:XX +0x98
==================
*/
```

### 2.3 常见的竞态条件模式

```go
// 模式1：共享变量的非原子操作
var sharedVar int

func pattern1Race() {
    go func() { sharedVar++ }()
    go func() { sharedVar++ }()
}

// 模式2：map的并发读写
var sharedMap = make(map[string]int)

func pattern2Race() {
    go func() { sharedMap["key"] = 1 }()
    go func() { fmt.Println(sharedMap["key"]) }()
}

// 模式3：slice的并发修改
var sharedSlice []int

func pattern3Race() {
    go func() { sharedSlice = append(sharedSlice, 1) }()
    go func() { sharedSlice = append(sharedSlice, 2) }()
}

// 模式4：channel的不当使用
func pattern4Race() {
    ch := make(chan int, 1)
    var closed bool
    
    go func() {
        if !closed {
            close(ch) // 竞态条件
            closed = true
        }
    }()
    
    go func() {
        if !closed {
            ch <- 1 // 可能向已关闭的channel发送
        }
    }()
}
```

## 3. 并发安全的数据结构

### 3.1 并发安全的Map

```go
import "sync"

// 使用sync.Map
func safeConcurrentMap() {
    var sm sync.Map
    var wg sync.WaitGroup
    
    // 并发写入
    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            sm.Store(fmt.Sprintf("key%d", id), id)
        }(i)
    }
    
    // 并发读取
    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            if value, ok := sm.Load(fmt.Sprintf("key%d", id)); ok {
                fmt.Printf("key%d: %v\n", id, value)
            }
        }(i)
    }
    
    wg.Wait()
}

// 自定义并发安全的Map
type SafeMap struct {
    mu   sync.RWMutex
    data map[string]interface{}
}

func NewSafeMap() *SafeMap {
    return &SafeMap{
        data: make(map[string]interface{}),
    }
}

func (sm *SafeMap) Set(key string, value interface{}) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.data[key] = value
}

func (sm *SafeMap) Get(key string) (interface{}, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    value, ok := sm.data[key]
    return value, ok
}

func (sm *SafeMap) Delete(key string) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    delete(sm.data, key)
}

func (sm *SafeMap) Range(fn func(key string, value interface{}) bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    
    for k, v := range sm.data {
        if !fn(k, v) {
            break
        }
    }
}
```

### 3.2 并发安全的Slice

```go
import "sync"

type SafeSlice struct {
    mu   sync.RWMutex
    data []interface{}
}

func NewSafeSlice() *SafeSlice {
    return &SafeSlice{
        data: make([]interface{}, 0),
    }
}

func (ss *SafeSlice) Append(item interface{}) {
    ss.mu.Lock()
    defer ss.mu.Unlock()
    ss.data = append(ss.data, item)
}

func (ss *SafeSlice) Get(index int) (interface{}, bool) {
    ss.mu.RLock()
    defer ss.mu.RUnlock()
    
    if index < 0 || index >= len(ss.data) {
        return nil, false
    }
    return ss.data[index], true
}

func (ss *SafeSlice) Len() int {
    ss.mu.RLock()
    defer ss.mu.RUnlock()
    return len(ss.data)
}

func (ss *SafeSlice) Range(fn func(index int, value interface{}) bool) {
    ss.mu.RLock()
    defer ss.mu.RUnlock()
    
    for i, v := range ss.data {
        if !fn(i, v) {
            break
        }
    }
}

// 使用copy-on-write策略的并发安全slice
type COWSlice struct {
    mu   sync.RWMutex
    data []interface{}
}

func (cs *COWSlice) Append(item interface{}) {
    cs.mu.Lock()
    defer cs.mu.Unlock()
    
    // 创建新的slice副本
    newData := make([]interface{}, len(cs.data)+1)
    copy(newData, cs.data)
    newData[len(cs.data)] = item
    cs.data = newData
}

func (cs *COWSlice) Get(index int) (interface{}, bool) {
    cs.mu.RLock()
    defer cs.mu.RUnlock()
    
    if index < 0 || index >= len(cs.data) {
        return nil, false
    }
    return cs.data[index], true
}
```

### 3.3 并发安全的计数器

```go
import "sync/atomic"

// 原子计数器
type AtomicCounter struct {
    value int64
}

func (ac *AtomicCounter) Increment() int64 {
    return atomic.AddInt64(&ac.value, 1)
}

func (ac *AtomicCounter) Decrement() int64 {
    return atomic.AddInt64(&ac.value, -1)
}

func (ac *AtomicCounter) Get() int64 {
    return atomic.LoadInt64(&ac.value)
}

func (ac *AtomicCounter) Set(value int64) {
    atomic.StoreInt64(&ac.value, value)
}

// 分片计数器（减少竞争）
type ShardedCounter struct {
    shards []AtomicCounter
    mask   int64
}

func NewShardedCounter(shardCount int) *ShardedCounter {
    if shardCount&(shardCount-1) != 0 {
        panic("shard count must be power of 2")
    }
    
    return &ShardedCounter{
        shards: make([]AtomicCounter, shardCount),
        mask:   int64(shardCount - 1),
    }
}

func (sc *ShardedCounter) Increment() {
    shard := fastHash() & sc.mask
    sc.shards[shard].Increment()
}

func (sc *ShardedCounter) Get() int64 {
    var total int64
    for i := range sc.shards {
        total += sc.shards[i].Get()
    }
    return total
}

func fastHash() int64 {
    // 简化的哈希函数，实际应用中可以使用更好的哈希
    return int64(runtime.NumGoroutine())
}
```

## 4. 高级并发安全模式

### 4.1 单例模式的并发安全实现

```go
import "sync"

// 方法1：使用sync.Once（推荐）
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

func GetInstance() *Singleton {
    once.Do(func() {
        instance = &Singleton{data: "singleton"}
    })
    return instance
}

// 方法2：使用原子操作
var instancePtr unsafe.Pointer

func GetInstanceAtomic() *Singleton {
    if ptr := atomic.LoadPointer(&instancePtr); ptr != nil {
        return (*Singleton)(ptr)
    }
    
    // 慢路径：需要初始化
    newInstance := &Singleton{data: "singleton"}
    if atomic.CompareAndSwapPointer(&instancePtr, nil, unsafe.Pointer(newInstance)) {
        return newInstance
    }
    
    // 其他goroutine已经初始化了
    return (*Singleton)(atomic.LoadPointer(&instancePtr))
}
```

### 4.2 读写分离模式

```go
// 配置管理器：读多写少的场景
type ConfigManager struct {
    mu     sync.RWMutex
    config map[string]string
}

func NewConfigManager() *ConfigManager {
    return &ConfigManager{
        config: make(map[string]string),
    }
}

func (cm *ConfigManager) Get(key string) (string, bool) {
    cm.mu.RLock()
    defer cm.mu.RUnlock()
    value, ok := cm.config[key]
    return value, ok
}

func (cm *ConfigManager) Set(key, value string) {
    cm.mu.Lock()
    defer cm.mu.Unlock()
    cm.config[key] = value
}

func (cm *ConfigManager) GetAll() map[string]string {
    cm.mu.RLock()
    defer cm.mu.RUnlock()
    
    // 返回副本，避免外部修改
    result := make(map[string]string)
    for k, v := range cm.config {
        result[k] = v
    }
    return result
}

// 使用原子操作的配置管理器
type AtomicConfigManager struct {
    configPtr unsafe.Pointer
}

func NewAtomicConfigManager() *AtomicConfigManager {
    config := make(map[string]string)
    acm := &AtomicConfigManager{}
    atomic.StorePointer(&acm.configPtr, unsafe.Pointer(&config))
    return acm
}

func (acm *AtomicConfigManager) Get(key string) (string, bool) {
    configPtr := atomic.LoadPointer(&acm.configPtr)
    config := *(*map[string]string)(configPtr)
    value, ok := config[key]
    return value, ok
}

func (acm *AtomicConfigManager) Set(key, value string) {
    for {
        oldPtr := atomic.LoadPointer(&acm.configPtr)
        oldConfig := *(*map[string]string)(oldPtr)
        
        // 创建新的配置副本
        newConfig := make(map[string]string)
        for k, v := range oldConfig {
            newConfig[k] = v
        }
        newConfig[key] = value
        
        if atomic.CompareAndSwapPointer(&acm.configPtr, oldPtr, unsafe.Pointer(&newConfig)) {
            break
        }
    }
}
```

### 4.3 生产者消费者模式

```go
// 并发安全的队列
type SafeQueue struct {
    mu    sync.Mutex
    cond  *sync.Cond
    items []interface{}
    closed bool
}

func NewSafeQueue() *SafeQueue {
    sq := &SafeQueue{
        items: make([]interface{}, 0),
    }
    sq.cond = sync.NewCond(&sq.mu)
    return sq
}

func (sq *SafeQueue) Enqueue(item interface{}) error {
    sq.mu.Lock()
    defer sq.mu.Unlock()
    
    if sq.closed {
        return fmt.Errorf("queue is closed")
    }
    
    sq.items = append(sq.items, item)
    sq.cond.Signal() // 通知等待的消费者
    return nil
}

func (sq *SafeQueue) Dequeue() (interface{}, bool) {
    sq.mu.Lock()
    defer sq.mu.Unlock()
    
    for len(sq.items) == 0 && !sq.closed {
        sq.cond.Wait() // 等待生产者
    }
    
    if len(sq.items) == 0 {
        return nil, false // 队列已关闭且为空
    }
    
    item := sq.items[0]
    sq.items = sq.items[1:]
    return item, true
}

func (sq *SafeQueue) Close() {
    sq.mu.Lock()
    defer sq.mu.Unlock()
    
    sq.closed = true
    sq.cond.Broadcast() // 通知所有等待的goroutine
}

// 使用示例
func producerConsumerExample() {
    queue := NewSafeQueue()
    var wg sync.WaitGroup
    
    // 生产者
    for i := 0; i < 3; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            for j := 0; j < 10; j++ {
                item := fmt.Sprintf("item-%d-%d", id, j)
                queue.Enqueue(item)
                fmt.Printf("Producer %d: produced %s\n", id, item)
                time.Sleep(time.Millisecond * 100)
            }
        }(i)
    }
    
    // 消费者
    for i := 0; i < 2; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            for {
                if item, ok := queue.Dequeue(); ok {
                    fmt.Printf("Consumer %d: consumed %v\n", id, item)
                } else {
                    break // 队列已关闭
                }
            }
        }(i)
    }
    
    // 等待生产者完成，然后关闭队列
    go func() {
        wg.Wait()
        queue.Close()
    }()
    
    time.Sleep(time.Second * 5)
}
```

## 5. 性能测试与基准测试

### 5.1 并发安全性能对比

```go
import "testing"

func BenchmarkUnsafeCounter(b *testing.B) {
    var counter int64
    
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            counter++ // 不安全，但快
        }
    })
}

func BenchmarkAtomicCounter(b *testing.B) {
    var counter int64
    
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            atomic.AddInt64(&counter, 1)
        }
    })
}

func BenchmarkMutexCounter(b *testing.B) {
    var counter int64
    var mu sync.Mutex
    
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            mu.Lock()
            counter++
            mu.Unlock()
        }
    })
}

func BenchmarkShardedCounter(b *testing.B) {
    sc := NewShardedCounter(16)
    
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            sc.Increment()
        }
    })
}
```

## 6. 面试重点问题

### Q1: 如何检测和避免竞态条件？
- **检测**：使用`go run -race`启用竞态检测器
- **避免**：使用适当的同步原语（mutex、channel、atomic）
- **设计**：尽量避免共享状态，使用消息传递

### Q2: sync.Map和普通map+mutex的区别？
- **sync.Map**：针对读多写少场景优化，内部使用分离的读写map
- **map+mutex**：简单直接，适合读写比例相近的场景
- **性能**：sync.Map在读多写少时性能更好

### Q3: 原子操作的适用场景？
- **简单数值操作**：计数器、标志位
- **指针操作**：配置更新、单例模式
- **性能要求高**：避免锁的开销
- **不适用**：复杂逻辑、多个变量的原子性

### Q4: 如何设计并发安全的数据结构？
1. **明确并发需求**：读写比例、性能要求
2. **选择同步策略**：锁、原子操作、无锁算法
3. **考虑性能优化**：分片、读写分离、copy-on-write
4. **充分测试**：单元测试、竞态检测、压力测试

这些并发安全技术是构建可靠Go应用的基础，掌握它们对于系统稳定性至关重要。
