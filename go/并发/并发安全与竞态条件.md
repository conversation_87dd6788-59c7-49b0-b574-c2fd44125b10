# 并发安全与竞态条件

## 竞态条件基础

### 1. 什么是竞态条件

```go
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

// 竞态条件示例
var unsafeCounter int

func demonstrateRaceCondition() {
    fmt.Println("=== 竞态条件演示 ===")
    
    const numGoroutines = 1000
    const numIncrements = 1000
    
    var wg sync.WaitGroup
    
    // 重置计数器
    unsafeCounter = 0
    
    start := time.Now()
    
    // 启动多个goroutine同时修改共享变量
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < numIncrements; j++ {
                // 这里存在竞态条件
                unsafeCounter++ // 非原子操作：读取->增加->写入
            }
        }()
    }
    
    wg.Wait()
    duration := time.Since(start)
    
    expected := numGoroutines * numIncrements
    fmt.Printf("期望结果: %d\n", expected)
    fmt.Printf("实际结果: %d\n", unsafeCounter)
    fmt.Printf("数据丢失: %d\n", expected-unsafeCounter)
    fmt.Printf("执行时间: %v\n", duration)
    
    if unsafeCounter != expected {
        fmt.Println("❌ 检测到竞态条件！")
    } else {
        fmt.Println("✅ 未检测到竞态条件（可能是运气好）")
    }
}

// 使用竞态检测器
func runWithRaceDetector() {
    fmt.Println("=== 使用竞态检测器 ===")
    fmt.Println("运行命令: go run -race main.go")
    fmt.Println("或编译: go build -race main.go")
    
    // 故意创建竞态条件
    var data int
    var wg sync.WaitGroup
    
    wg.Add(2)
    
    // 写入goroutine
    go func() {
        defer wg.Done()
        for i := 0; i < 100; i++ {
            data = i // 写入操作
            runtime.Gosched()
        }
    }()
    
    // 读取goroutine
    go func() {
        defer wg.Done()
        for i := 0; i < 100; i++ {
            _ = data // 读取操作
            runtime.Gosched()
        }
    }()
    
    wg.Wait()
    fmt.Printf("最终数据值: %d\n", data)
}
```

### 2. 竞态条件的危害

```go
// 银行账户转账示例 - 展示竞态条件的危害
type BankAccount struct {
    balance int
    mu      sync.Mutex // 用于后续的安全版本
}

func (ba *BankAccount) UnsafeDeposit(amount int) {
    // 不安全的存款操作
    currentBalance := ba.balance
    time.Sleep(time.Microsecond) // 模拟处理时间
    ba.balance = currentBalance + amount
}

func (ba *BankAccount) UnsafeWithdraw(amount int) bool {
    // 不安全的取款操作
    if ba.balance >= amount {
        currentBalance := ba.balance
        time.Sleep(time.Microsecond) // 模拟处理时间
        ba.balance = currentBalance - amount
        return true
    }
    return false
}

func (ba *BankAccount) SafeDeposit(amount int) {
    ba.mu.Lock()
    defer ba.mu.Unlock()
    ba.balance += amount
}

func (ba *BankAccount) SafeWithdraw(amount int) bool {
    ba.mu.Lock()
    defer ba.mu.Unlock()
    if ba.balance >= amount {
        ba.balance -= amount
        return true
    }
    return false
}

func (ba *BankAccount) GetBalance() int {
    ba.mu.Lock()
    defer ba.mu.Unlock()
    return ba.balance
}

func demonstrateBankingRaceCondition() {
    fmt.Println("=== 银行转账竞态条件演示 ===")
    
    // 不安全版本
    unsafeAccount := &BankAccount{balance: 1000}
    var wg sync.WaitGroup
    
    // 模拟并发存取款
    for i := 0; i < 100; i++ {
        wg.Add(2)
        
        // 存款goroutine
        go func() {
            defer wg.Done()
            unsafeAccount.UnsafeDeposit(10)
        }()
        
        // 取款goroutine
        go func() {
            defer wg.Done()
            unsafeAccount.UnsafeWithdraw(5)
        }()
    }
    
    wg.Wait()
    fmt.Printf("不安全账户最终余额: %d (期望: %d)\n", 
        unsafeAccount.balance, 1000+100*10-100*5)
    
    // 安全版本
    safeAccount := &BankAccount{balance: 1000}
    
    for i := 0; i < 100; i++ {
        wg.Add(2)
        
        go func() {
            defer wg.Done()
            safeAccount.SafeDeposit(10)
        }()
        
        go func() {
            defer wg.Done()
            safeAccount.SafeWithdraw(5)
        }()
    }
    
    wg.Wait()
    fmt.Printf("安全账户最终余额: %d (期望: %d)\n", 
        safeAccount.GetBalance(), 1000+100*10-100*5)
}
```

## 并发安全解决方案

### 1. 互斥锁（Mutex）

```go
// 互斥锁使用示例
type SafeCounter struct {
    mu    sync.Mutex
    value int
}

func (c *SafeCounter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.value++
}

func (c *SafeCounter) Decrement() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.value--
}

func (c *SafeCounter) Value() int {
    c.mu.Lock()
    defer c.mu.Unlock()
    return c.value
}

// 读写锁示例
type SafeMap struct {
    mu   sync.RWMutex
    data map[string]int
}

func NewSafeMap() *SafeMap {
    return &SafeMap{
        data: make(map[string]int),
    }
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.data[key] = value
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    value, exists := sm.data[key]
    return value, exists
}

func (sm *SafeMap) Delete(key string) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    delete(sm.data, key)
}

func demonstrateMutexUsage() {
    fmt.Println("=== 互斥锁使用演示 ===")
    
    counter := &SafeCounter{}
    var wg sync.WaitGroup
    
    // 并发增加
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter.Increment()
        }()
    }
    
    wg.Wait()
    fmt.Printf("安全计数器最终值: %d\n", counter.Value())
    
    // 读写锁演示
    safeMap := NewSafeMap()
    
    // 并发写入
    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(i int) {
            defer wg.Done()
            key := fmt.Sprintf("key%d", i)
            safeMap.Set(key, i)
        }(i)
    }
    
    // 并发读取
    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(i int) {
            defer wg.Done()
            key := fmt.Sprintf("key%d", i)
            if value, exists := safeMap.Get(key); exists {
                fmt.Printf("读取 %s: %d\n", key, value)
            }
        }(i)
    }
    
    wg.Wait()
}
```

### 2. 原子操作

```go
import (
    "sync/atomic"
)

// 原子操作示例
type AtomicCounter struct {
    value int64
}

func (c *AtomicCounter) Increment() {
    atomic.AddInt64(&c.value, 1)
}

func (c *AtomicCounter) Decrement() {
    atomic.AddInt64(&c.value, -1)
}

func (c *AtomicCounter) Value() int64 {
    return atomic.LoadInt64(&c.value)
}

func (c *AtomicCounter) CompareAndSwap(old, new int64) bool {
    return atomic.CompareAndSwapInt64(&c.value, old, new)
}

func demonstrateAtomicOperations() {
    fmt.Println("=== 原子操作演示 ===")
    
    counter := &AtomicCounter{}
    var wg sync.WaitGroup
    
    start := time.Now()
    
    // 并发增加
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter.Increment()
        }()
    }
    
    wg.Wait()
    duration := time.Since(start)
    
    fmt.Printf("原子计数器最终值: %d\n", counter.Value())
    fmt.Printf("执行时间: %v\n", duration)
    
    // CAS操作演示
    fmt.Println("=== CAS操作演示 ===")
    current := counter.Value()
    newValue := current + 100
    
    if counter.CompareAndSwap(current, newValue) {
        fmt.Printf("CAS成功: %d -> %d\n", current, newValue)
    } else {
        fmt.Printf("CAS失败，当前值: %d\n", counter.Value())
    }
}
```

### 3. Channel同步

```go
// 使用Channel实现并发安全
type ChannelCounter struct {
    ch chan int
    value int
}

func NewChannelCounter() *ChannelCounter {
    cc := &ChannelCounter{
        ch: make(chan int),
        value: 0,
    }
    
    // 启动处理goroutine
    go cc.process()
    return cc
}

func (cc *ChannelCounter) process() {
    for delta := range cc.ch {
        cc.value += delta
    }
}

func (cc *ChannelCounter) Increment() {
    cc.ch <- 1
}

func (cc *ChannelCounter) Decrement() {
    cc.ch <- -1
}

func (cc *ChannelCounter) Value() int {
    // 这里需要特殊处理来获取值
    result := make(chan int)
    go func() {
        // 发送特殊信号获取当前值
        select {
        case cc.ch <- 0: // 发送0表示查询
            result <- cc.value
        }
    }()
    return <-result
}

func demonstrateChannelSynchronization() {
    fmt.Println("=== Channel同步演示 ===")
    
    counter := NewChannelCounter()
    var wg sync.WaitGroup
    
    // 并发操作
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter.Increment()
        }()
    }
    
    wg.Wait()
    time.Sleep(100 * time.Millisecond) // 等待处理完成
    
    fmt.Printf("Channel计数器最终值: %d\n", counter.Value())
}
```

## 性能对比

### 1. 不同同步机制的性能对比

```go
func benchmarkSynchronizationMethods() {
    fmt.Println("=== 同步机制性能对比 ===")
    
    const numOperations = 100000
    const numGoroutines = 100
    
    // 1. 互斥锁性能测试
    mutexCounter := &SafeCounter{}
    start := time.Now()
    
    var wg sync.WaitGroup
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < numOperations/numGoroutines; j++ {
                mutexCounter.Increment()
            }
        }()
    }
    wg.Wait()
    mutexDuration := time.Since(start)
    
    // 2. 原子操作性能测试
    atomicCounter := &AtomicCounter{}
    start = time.Now()
    
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < numOperations/numGoroutines; j++ {
                atomicCounter.Increment()
            }
        }()
    }
    wg.Wait()
    atomicDuration := time.Since(start)
    
    fmt.Printf("互斥锁方式 - 结果: %d, 耗时: %v\n", 
        mutexCounter.Value(), mutexDuration)
    fmt.Printf("原子操作方式 - 结果: %d, 耗时: %v\n", 
        atomicCounter.Value(), atomicDuration)
    fmt.Printf("性能提升: %.2fx\n", 
        float64(mutexDuration)/float64(atomicDuration))
}
```

## 常见并发问题

### 1. 死锁检测和避免

```go
// 死锁示例和避免方法
func demonstrateDeadlock() {
    fmt.Println("=== 死锁演示和避免 ===")
    
    // 死锁示例（注释掉避免程序卡死）
    /*
    var mu1, mu2 sync.Mutex
    
    go func() {
        mu1.Lock()
        fmt.Println("Goroutine 1: 获得锁1")
        time.Sleep(100 * time.Millisecond)
        
        mu2.Lock() // 等待锁2
        fmt.Println("Goroutine 1: 获得锁2")
        mu2.Unlock()
        mu1.Unlock()
    }()
    
    go func() {
        mu2.Lock()
        fmt.Println("Goroutine 2: 获得锁2")
        time.Sleep(100 * time.Millisecond)
        
        mu1.Lock() // 等待锁1，形成死锁
        fmt.Println("Goroutine 2: 获得锁1")
        mu1.Unlock()
        mu2.Unlock()
    }()
    */
    
    // 避免死锁的方法1: 锁排序
    var mu1, mu2 sync.Mutex
    var wg sync.WaitGroup
    
    lockInOrder := func(first, second *sync.Mutex, name string) {
        defer wg.Done()
        first.Lock()
        fmt.Printf("%s: 获得第一个锁\n", name)
        
        second.Lock()
        fmt.Printf("%s: 获得第二个锁\n", name)
        
        // 模拟工作
        time.Sleep(50 * time.Millisecond)
        
        second.Unlock()
        first.Unlock()
        fmt.Printf("%s: 释放所有锁\n", name)
    }
    
    wg.Add(2)
    go lockInOrder(&mu1, &mu2, "Goroutine 1")
    go lockInOrder(&mu1, &mu2, "Goroutine 2") // 相同的锁顺序
    
    wg.Wait()
    
    // 避免死锁的方法2: 超时机制
    demonstrateTimeoutLocking()
}

func demonstrateTimeoutLocking() {
    fmt.Println("=== 超时锁机制 ===")
    
    type TimedMutex struct {
        ch chan struct{}
    }
    
    func NewTimedMutex() *TimedMutex {
        return &TimedMutex{
            ch: make(chan struct{}, 1),
        }
    }
    
    func (tm *TimedMutex) Lock() {
        tm.ch <- struct{}{}
    }
    
    func (tm *TimedMutex) TryLock(timeout time.Duration) bool {
        select {
        case tm.ch <- struct{}{}:
            return true
        case <-time.After(timeout):
            return false
        }
    }
    
    func (tm *TimedMutex) Unlock() {
        <-tm.ch
    }
    
    timedMutex := NewTimedMutex()
    var wg sync.WaitGroup
    
    wg.Add(2)
    
    go func() {
        defer wg.Done()
        timedMutex.Lock()
        fmt.Println("Goroutine 1: 获得锁")
        time.Sleep(200 * time.Millisecond)
        timedMutex.Unlock()
        fmt.Println("Goroutine 1: 释放锁")
    }()
    
    go func() {
        defer wg.Done()
        time.Sleep(50 * time.Millisecond)
        
        if timedMutex.TryLock(100 * time.Millisecond) {
            fmt.Println("Goroutine 2: 获得锁")
            timedMutex.Unlock()
        } else {
            fmt.Println("Goroutine 2: 获取锁超时")
        }
    }()
    
    wg.Wait()
}

func main() {
    demonstrateRaceCondition()
    fmt.Println()
    runWithRaceDetector()
    fmt.Println()
    demonstrateBankingRaceCondition()
    fmt.Println()
    demonstrateMutexUsage()
    fmt.Println()
    demonstrateAtomicOperations()
    fmt.Println()
    demonstrateChannelSynchronization()
    fmt.Println()
    benchmarkSynchronizationMethods()
    fmt.Println()
    demonstrateDeadlock()
}
```

## 面试要点总结

1. **竞态条件识别**: 能够识别和理解竞态条件的产生原因
2. **同步机制选择**: 掌握互斥锁、读写锁、原子操作、Channel等同步方式的适用场景
3. **性能考量**: 了解不同同步机制的性能特点和开销
4. **死锁避免**: 掌握死锁的产生条件和避免方法
5. **竞态检测**: 熟悉Go的竞态检测器使用方法
6. **最佳实践**: 遵循并发安全的编程规范和模式
7. **调试技能**: 能够诊断和解决并发相关的问题
