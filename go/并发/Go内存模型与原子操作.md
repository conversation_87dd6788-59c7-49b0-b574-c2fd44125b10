# Go内存模型与原子操作

## Go内存模型基础

### 1. 内存模型概念

#### 什么是内存模型？
Go内存模型定义了在并发程序中，一个goroutine对变量的写操作何时能被另一个goroutine的读操作观察到。

#### 核心原则
- **单个goroutine内**：程序按照源代码顺序执行
- **多个goroutine间**：需要使用同步原语来保证内存可见性
- **数据竞争**：同时访问同一内存位置且至少有一个是写操作

### 2. Happens-Before关系

#### 定义
如果事件e1 happens-before 事件e2，那么e1的效果对e2可见。

#### 基本规则
```go
// 规则1：单个goroutine内的顺序
func singleGoroutine() {
    a := 1  // happens-before
    b := 2  // happens-before
    c := a + b // 这里能看到a和b的值
}

// 规则2：goroutine创建
var x int

func main() {
    x = 1 // happens-before goroutine启动
    go func() {
        fmt.Println(x) // 能看到x=1
    }()
}

// 规则3：channel操作
func channelExample() {
    ch := make(chan int)
    var data int
    
    go func() {
        data = 42      // happens-before channel发送
        ch <- 1        // 发送操作
    }()
    
    <-ch               // 接收操作 happens-before
    fmt.Println(data)  // 能看到data=42
}
```

### 3. 数据竞争示例

#### 典型的数据竞争
```go
// 错误示例：存在数据竞争
var counter int

func badIncrement() {
    for i := 0; i < 1000; i++ {
        go func() {
            counter++ // 数据竞争！
        }()
    }
    time.Sleep(time.Second)
    fmt.Println("Counter:", counter) // 结果不确定
}

// 正确示例：使用互斥锁
var (
    counter int
    mu      sync.Mutex
)

func goodIncrement() {
    for i := 0; i < 1000; i++ {
        go func() {
            mu.Lock()
            counter++
            mu.Unlock()
        }()
    }
    time.Sleep(time.Second)
    fmt.Println("Counter:", counter) // 结果确定为1000
}
```

## 原子操作详解

### 1. sync/atomic包

#### 基本原子操作
```go
import "sync/atomic"

// 原子加法
func atomicAdd() {
    var counter int64
    
    // 启动多个goroutine进行原子加法
    var wg sync.WaitGroup
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            atomic.AddInt64(&counter, 1)
        }()
    }
    
    wg.Wait()
    fmt.Println("Counter:", atomic.LoadInt64(&counter))
}

// 原子比较并交换
func atomicCAS() {
    var value int64 = 10
    
    // 如果value等于10，则设置为20
    swapped := atomic.CompareAndSwapInt64(&value, 10, 20)
    fmt.Printf("Swapped: %t, Value: %d\n", swapped, value)
    
    // 再次尝试，这次应该失败
    swapped = atomic.CompareAndSwapInt64(&value, 10, 30)
    fmt.Printf("Swapped: %t, Value: %d\n", swapped, value)
}

// 原子存储和加载
func atomicStoreLoad() {
    var config int64
    
    // 原子存储
    atomic.StoreInt64(&config, 42)
    
    // 原子加载
    value := atomic.LoadInt64(&config)
    fmt.Println("Config value:", value)
}
```

### 2. 原子操作的应用场景

#### 无锁计数器
```go
type AtomicCounter struct {
    value int64
}

func (c *AtomicCounter) Increment() int64 {
    return atomic.AddInt64(&c.value, 1)
}

func (c *AtomicCounter) Decrement() int64 {
    return atomic.AddInt64(&c.value, -1)
}

func (c *AtomicCounter) Get() int64 {
    return atomic.LoadInt64(&c.value)
}

func (c *AtomicCounter) Set(value int64) {
    atomic.StoreInt64(&c.value, value)
}

func (c *AtomicCounter) CompareAndSwap(old, new int64) bool {
    return atomic.CompareAndSwapInt64(&c.value, old, new)
}
```

#### 无锁标志位
```go
type AtomicFlag struct {
    flag int32
}

func (f *AtomicFlag) Set() {
    atomic.StoreInt32(&f.flag, 1)
}

func (f *AtomicFlag) Clear() {
    atomic.StoreInt32(&f.flag, 0)
}

func (f *AtomicFlag) IsSet() bool {
    return atomic.LoadInt32(&f.flag) == 1
}

func (f *AtomicFlag) TestAndSet() bool {
    return atomic.SwapInt32(&f.flag, 1) == 1
}
```

#### 无锁配置更新
```go
type Config struct {
    Host string
    Port int
}

type AtomicConfig struct {
    config unsafe.Pointer
}

func NewAtomicConfig(cfg *Config) *AtomicConfig {
    ac := &AtomicConfig{}
    ac.Store(cfg)
    return ac
}

func (ac *AtomicConfig) Store(cfg *Config) {
    atomic.StorePointer(&ac.config, unsafe.Pointer(cfg))
}

func (ac *AtomicConfig) Load() *Config {
    return (*Config)(atomic.LoadPointer(&ac.config))
}

func (ac *AtomicConfig) Update(updater func(*Config) *Config) {
    for {
        old := ac.Load()
        new := updater(old)
        if atomic.CompareAndSwapPointer(&ac.config, 
                                       unsafe.Pointer(old), 
                                       unsafe.Pointer(new)) {
            break
        }
    }
}
```

### 3. 原子操作vs互斥锁

#### 性能对比
```go
func benchmarkAtomic(b *testing.B) {
    var counter int64
    
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            atomic.AddInt64(&counter, 1)
        }
    })
}

func benchmarkMutex(b *testing.B) {
    var counter int64
    var mu sync.Mutex
    
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            mu.Lock()
            counter++
            mu.Unlock()
        }
    })
}

// 结果：原子操作通常比互斥锁快2-10倍
```

#### 适用场景对比
| 场景 | 原子操作 | 互斥锁 |
|------|----------|--------|
| 简单数值操作 | ✅ 推荐 | ❌ 过重 |
| 复杂逻辑保护 | ❌ 不适用 | ✅ 推荐 |
| 多个变量同步 | ❌ 困难 | ✅ 简单 |
| 性能要求高 | ✅ 更快 | ❌ 较慢 |
| 代码可读性 | ❌ 复杂 | ✅ 清晰 |

## 高级并发模式

### 1. 无锁数据结构

#### 无锁栈
```go
type LockFreeStack struct {
    head unsafe.Pointer
}

type stackNode struct {
    data interface{}
    next unsafe.Pointer
}

func (s *LockFreeStack) Push(data interface{}) {
    node := &stackNode{data: data}
    
    for {
        head := atomic.LoadPointer(&s.head)
        node.next = head
        
        if atomic.CompareAndSwapPointer(&s.head, head, unsafe.Pointer(node)) {
            break
        }
    }
}

func (s *LockFreeStack) Pop() interface{} {
    for {
        head := atomic.LoadPointer(&s.head)
        if head == nil {
            return nil
        }
        
        node := (*stackNode)(head)
        next := atomic.LoadPointer(&node.next)
        
        if atomic.CompareAndSwapPointer(&s.head, head, next) {
            return node.data
        }
    }
}
```

#### 无锁队列（简化版）
```go
type LockFreeQueue struct {
    head unsafe.Pointer
    tail unsafe.Pointer
}

type queueNode struct {
    data interface{}
    next unsafe.Pointer
}

func NewLockFreeQueue() *LockFreeQueue {
    node := &queueNode{}
    q := &LockFreeQueue{
        head: unsafe.Pointer(node),
        tail: unsafe.Pointer(node),
    }
    return q
}

func (q *LockFreeQueue) Enqueue(data interface{}) {
    node := &queueNode{data: data}
    
    for {
        tail := atomic.LoadPointer(&q.tail)
        tailNode := (*queueNode)(tail)
        next := atomic.LoadPointer(&tailNode.next)
        
        if tail == atomic.LoadPointer(&q.tail) {
            if next == nil {
                if atomic.CompareAndSwapPointer(&tailNode.next, nil, unsafe.Pointer(node)) {
                    atomic.CompareAndSwapPointer(&q.tail, tail, unsafe.Pointer(node))
                    break
                }
            } else {
                atomic.CompareAndSwapPointer(&q.tail, tail, next)
            }
        }
    }
}
```

### 2. 内存屏障和可见性

#### 内存屏障示例
```go
// 使用atomic操作作为内存屏障
var ready int32
var data int

func writer() {
    data = 42                           // 1. 写入数据
    atomic.StoreInt32(&ready, 1)        // 2. 原子写入标志（内存屏障）
}

func reader() {
    for atomic.LoadInt32(&ready) == 0 { // 3. 原子读取标志（内存屏障）
        runtime.Gosched()
    }
    fmt.Println(data)                   // 4. 读取数据（保证能看到42）
}
```

#### 双重检查锁定模式
```go
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

// 使用sync.Once（推荐）
func GetInstance() *Singleton {
    once.Do(func() {
        instance = &Singleton{data: "singleton"}
    })
    return instance
}

// 手动实现双重检查（不推荐，仅作示例）
var (
    instance2 unsafe.Pointer
    mu2       sync.Mutex
)

func GetInstance2() *Singleton {
    // 第一次检查（无锁）
    if atomic.LoadPointer(&instance2) == nil {
        mu2.Lock()
        defer mu2.Unlock()
        
        // 第二次检查（有锁）
        if atomic.LoadPointer(&instance2) == nil {
            s := &Singleton{data: "singleton"}
            atomic.StorePointer(&instance2, unsafe.Pointer(s))
        }
    }
    
    return (*Singleton)(atomic.LoadPointer(&instance2))
}
```

### 3. 内存对齐和缓存行

#### 缓存行填充
```go
// 避免伪共享的结构体设计
type PaddedCounter struct {
    value int64
    _     [7]int64 // 填充到64字节（一个缓存行）
}

type Counters struct {
    counter1 PaddedCounter
    counter2 PaddedCounter
    counter3 PaddedCounter
    counter4 PaddedCounter
}

func (c *Counters) Increment(index int) {
    switch index {
    case 0:
        atomic.AddInt64(&c.counter1.value, 1)
    case 1:
        atomic.AddInt64(&c.counter2.value, 1)
    case 2:
        atomic.AddInt64(&c.counter3.value, 1)
    case 3:
        atomic.AddInt64(&c.counter4.value, 1)
    }
}
```

#### 内存对齐检查
```go
import "unsafe"

func checkAlignment() {
    type TestStruct struct {
        a int8   // 1 byte
        b int64  // 8 bytes
        c int8   // 1 byte
    }
    
    fmt.Printf("TestStruct size: %d\n", unsafe.Sizeof(TestStruct{}))
    fmt.Printf("Field a offset: %d\n", unsafe.Offsetof(TestStruct{}.a))
    fmt.Printf("Field b offset: %d\n", unsafe.Offsetof(TestStruct{}.b))
    fmt.Printf("Field c offset: %d\n", unsafe.Offsetof(TestStruct{}.c))
    
    // 优化后的结构体
    type OptimizedStruct struct {
        b int64  // 8 bytes
        a int8   // 1 byte
        c int8   // 1 byte
    }
    
    fmt.Printf("OptimizedStruct size: %d\n", unsafe.Sizeof(OptimizedStruct{}))
}
```

## 实际应用案例

### 1. 高性能计数器
```go
type HighPerformanceCounter struct {
    counters []PaddedCounter
    mask     int64
}

func NewHighPerformanceCounter(shards int) *HighPerformanceCounter {
    // 确保shards是2的幂
    if shards&(shards-1) != 0 {
        panic("shards must be power of 2")
    }
    
    return &HighPerformanceCounter{
        counters: make([]PaddedCounter, shards),
        mask:     int64(shards - 1),
    }
}

func (hpc *HighPerformanceCounter) Increment() {
    // 使用goroutine ID的哈希来选择分片
    shard := fastrand() & hpc.mask
    atomic.AddInt64(&hpc.counters[shard].value, 1)
}

func (hpc *HighPerformanceCounter) Get() int64 {
    var total int64
    for i := range hpc.counters {
        total += atomic.LoadInt64(&hpc.counters[i].value)
    }
    return total
}

// 简化的快速随机数生成器
func fastrand() int64 {
    // 实际实现会更复杂
    return int64(uintptr(unsafe.Pointer(&struct{}{})))
}
```

### 2. 无锁环形缓冲区
```go
type RingBuffer struct {
    buffer []interface{}
    mask   int64
    head   int64
    tail   int64
}

func NewRingBuffer(size int) *RingBuffer {
    if size&(size-1) != 0 {
        panic("size must be power of 2")
    }
    
    return &RingBuffer{
        buffer: make([]interface{}, size),
        mask:   int64(size - 1),
    }
}

func (rb *RingBuffer) Put(item interface{}) bool {
    head := atomic.LoadInt64(&rb.head)
    tail := atomic.LoadInt64(&rb.tail)
    
    if head-tail >= int64(len(rb.buffer)) {
        return false // 缓冲区满
    }
    
    rb.buffer[head&rb.mask] = item
    atomic.StoreInt64(&rb.head, head+1)
    return true
}

func (rb *RingBuffer) Get() (interface{}, bool) {
    head := atomic.LoadInt64(&rb.head)
    tail := atomic.LoadInt64(&rb.tail)
    
    if head == tail {
        return nil, false // 缓冲区空
    }
    
    item := rb.buffer[tail&rb.mask]
    atomic.StoreInt64(&rb.tail, tail+1)
    return item, true
}
```

## 调试和性能分析

### 1. 竞争检测
```bash
# 编译时启用竞争检测
go build -race main.go

# 运行时启用竞争检测
go run -race main.go

# 测试时启用竞争检测
go test -race
```

### 2. 内存分析
```go
import _ "net/http/pprof"

func main() {
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 你的程序逻辑
}

// 命令行分析：
// go tool pprof http://localhost:6060/debug/pprof/heap
// go tool pprof http://localhost:6060/debug/pprof/goroutine
```

## 面试要点总结

1. **内存模型理解**：掌握happens-before关系和数据竞争概念
2. **原子操作应用**：了解各种原子操作的使用场景和性能特点
3. **无锁编程**：理解无锁数据结构的设计原理和实现难点
4. **内存可见性**：掌握内存屏障和缓存一致性问题
5. **性能优化**：了解缓存行对齐、伪共享等性能优化技巧
6. **调试技能**：能够使用竞争检测器和性能分析工具
7. **实际应用**：能够在实际项目中正确使用并发原语
