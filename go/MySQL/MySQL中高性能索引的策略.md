# MySQL中高性能索引的策略

## 索引基础

### 索引原理
- **本质**：索引是排序的数据结构，用于快速定位数据
- **数据结构**：MySQL主要使用B+Tree索引
- **时间复杂度**：O(log n)，相比全表扫描O(n)大幅提升

### B+Tree特点
- **内部节点**：只存储键值，不存储数据
- **叶子节点**：存储所有数据，通过链表连接
- **优势**：范围查询效率高，磁盘I/O次数少

### 存储引擎差异

#### InnoDB（聚簇索引）
- **主键索引**：叶子节点存储完整行数据
- **二级索引**：叶子节点存储主键值，需要回表查询

#### MyISAM（非聚簇索引）
- **所有索引**：叶子节点存储数据行的物理地址
- **主键索引**：与普通索引结构相同

## 索引设计策略

### 最左前缀原则
联合索引`(a, b, c)`的使用规则：
```sql
-- 可以使用索引
WHERE a = 1
WHERE a = 1 AND b = 2
WHERE a = 1 AND b = 2 AND c = 3

-- 不能使用索引
WHERE b = 2
WHERE c = 3
WHERE b = 2 AND c = 3
```

### 索引选择性
- **高选择性**：区分度高的列适合建索引（如用户ID）
- **低选择性**：区分度低的列不适合建索引（如性别）
- **计算公式**：选择性 = 不重复值数量 / 总记录数

## 索引优化技巧

### 1. 覆盖索引
```sql
-- 查询字段都在索引中，避免回表
CREATE INDEX idx_cover ON orders(customer_id, order_date, amount);
SELECT customer_id, order_date, amount FROM orders WHERE customer_id = 123;
```

### 2. 前缀索引
```sql
-- 长字符串使用前缀索引节省空间
CREATE INDEX idx_email_prefix ON users(email(10));
```

### 3. 联合索引顺序
```sql
-- 等值查询在前，范围查询在后
CREATE INDEX idx_status_date ON orders(status, create_time);
WHERE status = 'paid' AND create_time > '2024-01-01';
```

## 索引失效场景

### 常见失效情况
1. **函数操作**：`WHERE YEAR(date) = 2024` → `WHERE date >= '2024-01-01'`
2. **类型转换**：`WHERE phone = 123` → `WHERE phone = '123'`
3. **否定条件**：`WHERE status != 'deleted'` → `WHERE status IN (...)`
4. **前缀模糊**：`WHERE name LIKE '%abc'` → `WHERE name LIKE 'abc%'`
5. **OR条件**：确保OR两边都有索引，或使用UNION
6. **联合索引断层**：跳过最左前缀的中间列

## 索引监控与维护

### 监控指标
```sql
-- 查看索引使用情况
SHOW INDEX FROM table_name;

-- 查看执行计划
EXPLAIN SELECT * FROM table_name WHERE condition;

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

### 维护操作
```sql
-- 分析表统计信息
ANALYZE TABLE table_name;

-- 优化表（整理碎片）
OPTIMIZE TABLE table_name;
```

## 高级特性

### MySQL 8.0新特性
- **函数索引**：`CREATE INDEX idx ON table((UPPER(col)))`
- **多值索引**：支持JSON数组索引
- **不可见索引**：`CREATE INDEX idx ON table(col) INVISIBLE`

## 面试常见问题

### Q1: 什么时候不应该创建索引？
**答案**：
1. **小表**：数据量小时全表扫描更快
2. **频繁更新的列**：索引维护成本高
3. **低选择性列**：如性别字段，区分度低
4. **临时表**：生命周期短的表

### Q2: 联合索引的顺序如何确定？
**答案**：
1. **最左前缀原则**：最常用条件放最左边
2. **选择性高的列优先**：区分度高的列在前
3. **范围查询列放最后**：避免影响后续列使用

### Q3: 如何判断索引是否有效？
**答案**：
1. **EXPLAIN分析**：查看执行计划
2. **慢查询监控**：找出性能问题
3. **索引统计信息**：分析使用情况
4. **性能测试对比**：有无索引的差异

### Q4: 聚簇索引和非聚簇索引的区别？
**答案**：
- **聚簇索引**：数据和索引存储在一起，InnoDB主键索引
- **非聚簇索引**：索引和数据分开存储，MyISAM所有索引

## 总结

高性能索引策略要点：
1. **理解原理**：B+Tree结构和查询过程
2. **设计原则**：最左前缀、覆盖索引、选择性
3. **避免失效**：函数、类型转换、模糊查询等
4. **持续优化**：监控使用情况，定期维护