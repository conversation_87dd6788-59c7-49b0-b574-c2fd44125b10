# MongoDB聚合管道和性能优化

## 聚合管道基本概念

### 什么是聚合管道
聚合管道是MongoDB的数据处理框架，通过一系列阶段对数据进行过滤、分组、排序、计算等操作。

### 基本结构
```javascript
db.collection.aggregate([
    { $match: { status: "active" } },    // 过滤阶段
    { $group: { _id: "$category", total: { $sum: "$amount" } } }, // 分组阶段
    { $sort: { total: -1 } },            // 排序阶段
    { $limit: 10 }                       // 限制阶段
])
```

### Go语言实现
```go
type OrderStats struct {
    UserID      string  `bson:"_id"`
    TotalAmount float64 `bson:"total_amount"`
    OrderCount  int     `bson:"order_count"`
}

func AggregateOrderStats(collection *mongo.Collection) ([]OrderStats, error) {
    pipeline := []bson.M{
        // 过滤已完成订单
        {"$match": bson.M{"status": "completed"}},

        // 按用户分组统计
        {"$group": bson.M{
            "_id": "$user_id",
            "total_amount": bson.M{"$sum": "$amount"},
            "order_count":  bson.M{"$sum": 1},
        }},

        // 按总金额排序
        {"$sort": bson.M{"total_amount": -1}},

        // 限制返回数量
        {"$limit": 10},
    }

    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())

    var results []OrderStats
    err = cursor.All(context.Background(), &results)
    return results, err
}
```

## 常用聚合阶段

### $match阶段优化
**原则**：尽早过滤数据，减少后续处理量

```go
// 优化前：先分组再过滤（处理所有数据）
pipeline := []bson.M{
    {"$group": bson.M{"_id": "$category", "total": bson.M{"$sum": "$amount"}}},
    {"$match": bson.M{"total": bson.M{"$gt": 1000}}},
}

// 优化后：先过滤再分组（减少数据量）
pipeline := []bson.M{
    {"$match": bson.M{"status": "active"}}, // 先过滤
    {"$group": bson.M{"_id": "$category", "total": bson.M{"$sum": "$amount"}}},
    {"$match": bson.M{"total": bson.M{"$gt": 1000}}},
}
```

### $lookup阶段（关联查询）
**用途**：类似SQL的JOIN操作，关联不同集合的数据

```go
func GetUsersWithOrders(userCollection *mongo.Collection) ([]bson.M, error) {
    pipeline := []bson.M{
        // 关联订单数据
        {"$lookup": bson.M{
            "from":         "orders",
            "localField":   "_id",
            "foreignField": "user_id",
            "as":           "orders",
            "pipeline": []bson.M{
                {"$match": bson.M{"status": "completed"}},
                {"$sort": bson.M{"created_at": -1}},
                {"$limit": 5},
            },
        }},

        // 只返回有订单的用户
        {"$match": bson.M{"orders": bson.M{"$ne": []interface{}{}}}},

        // 添加统计字段
        {"$addFields": bson.M{
            "order_count": bson.M{"$size": "$orders"},
            "total_spent": bson.M{"$sum": "$orders.amount"},
        }},
    }

    cursor, err := userCollection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())

    var results []bson.M
    err = cursor.All(context.Background(), &results)
    return results, err
}
```

### $group阶段
**常用操作符**：
- `$sum`：求和
- `$avg`：平均值
- `$max/$min`：最大/最小值
- `$push`：将值添加到数组
- `$addToSet`：去重添加到数组

### $project阶段
**作用**：选择需要的字段，减少数据传输量
```go
{"$project": bson.M{
    "name": 1,
    "email": 1,
    "total_orders": bson.M{"$size": "$orders"},
    "_id": 0, // 排除_id字段
}}
```

### $facet阶段（多维度聚合）
**用途**：在同一个聚合管道中执行多个并行的聚合操作

```go
func GetDashboardStats(collection *mongo.Collection) (bson.M, error) {
    pipeline := []bson.M{
        {"$match": bson.M{"created_at": bson.M{"$gte": time.Now().AddDate(-1, 0, 0)}}},

        {"$facet": bson.M{
            // 按类别统计
            "sales_by_category": []bson.M{
                {"$group": bson.M{"_id": "$category", "total": bson.M{"$sum": "$amount"}}},
                {"$sort": bson.M{"total": -1}},
            },

            // 按月份统计
            "sales_by_month": []bson.M{
                {"$group": bson.M{
                    "_id": bson.M{"month": bson.M{"$month": "$created_at"}},
                    "total": bson.M{"$sum": "$amount"},
                }},
                {"$sort": bson.M{"_id": 1}},
            },

            // 总体统计
            "summary": []bson.M{
                {"$group": bson.M{
                    "_id": nil,
                    "total_sales": bson.M{"$sum": "$amount"},
                    "total_orders": bson.M{"$sum": 1},
                }},
            },
        }},
    }

    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())

    var results []bson.M
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }

    if len(results) > 0 {
        return results[0], nil
    }
    return nil, nil
}
```

## 性能优化策略

### 索引优化
**原则**：为聚合管道的关键阶段创建合适的索引

```go
// 创建聚合优化索引
func CreateAggregationIndexes(collection *mongo.Collection) error {
    indexes := []mongo.IndexModel{
        // 支持$match和$sort的复合索引
        {Keys: bson.D{{"status", 1}, {"created_at", -1}}},

        // 支持$group操作的索引
        {Keys: bson.D{{"user_id", 1}, {"amount", 1}}},

        // 支持$lookup操作的索引
        {Keys: bson.D{{"product_id", 1}}},
    }

    _, err := collection.Indexes().CreateMany(context.Background(), indexes)
    return err
}

// 使用explain分析性能
func ExplainAggregation(collection *mongo.Collection, pipeline []bson.M) {
    opts := options.Aggregate().SetExplain(true)
    cursor, _ := collection.Aggregate(context.Background(), pipeline, opts)
    defer cursor.Close(context.Background())

    var explanation bson.M
    if cursor.Next(context.Background()) {
        cursor.Decode(&explanation)
        fmt.Printf("Aggregation Explanation: %+v\n", explanation)
    }
}
```

### 内存使用优化
**问题**：聚合管道单阶段默认内存限制100MB
**解决方案**：

```go
// 1. 使用allowDiskUse处理大数据集
func LargeDataAggregation(collection *mongo.Collection) ([]bson.M, error) {
    pipeline := []bson.M{
        {"$match": bson.M{"status": "active"}},
        {"$group": bson.M{"_id": "$category", "total": bson.M{"$sum": "$amount"}}},
        {"$sort": bson.M{"total": -1}},
    }

    // 允许使用磁盘存储中间结果
    opts := options.Aggregate().SetAllowDiskUse(true)
    cursor, err := collection.Aggregate(context.Background(), pipeline, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())

    var results []bson.M
    err = cursor.All(context.Background(), &results)
    return results, err
}

// 2. 分批处理避免内存溢出
func BatchAggregation(collection *mongo.Collection, batchSize int) error {
    pipeline := []bson.M{
        {"$match": bson.M{"processed": false}},
        {"$limit": batchSize},
    }

    for {
        cursor, err := collection.Aggregate(context.Background(), pipeline)
        if err != nil {
            return err
        }

        var batch []bson.M
        err = cursor.All(context.Background(), &batch)
        cursor.Close(context.Background())

        if err != nil || len(batch) == 0 {
            break
        }

        // 处理批次数据
        processBatch(collection, batch)
    }
    return nil
}
```

### 阶段顺序优化
**优化原则**：
1. **$match尽早使用**：减少后续处理的数据量
2. **$project减少字段**：只保留必要字段
3. **$limit尽早应用**：限制处理的文档数量
4. **$sort配合索引**：利用索引排序

```go
// 优化的管道顺序
pipeline := []bson.M{
    {"$match": bson.M{"status": "active"}},           // 1. 先过滤
    {"$project": bson.M{"user_id": 1, "amount": 1}}, // 2. 减少字段
    {"$group": bson.M{"_id": "$user_id", "total": bson.M{"$sum": "$amount"}}},
    {"$sort": bson.M{"total": -1}},                   // 3. 排序
    {"$limit": 10},                                   // 4. 限制结果
}
```

### 时间序列数据聚合
**常见场景**：日志分析、监控数据统计

```go
func AggregateTimeSeriesData(collection *mongo.Collection, interval string) ([]bson.M, error) {
    var groupBy bson.M
    switch interval {
    case "hour":
        groupBy = bson.M{
            "year": bson.M{"$year": "$timestamp"},
            "month": bson.M{"$month": "$timestamp"},
            "day": bson.M{"$dayOfMonth": "$timestamp"},
            "hour": bson.M{"$hour": "$timestamp"},
        }
    case "day":
        groupBy = bson.M{
            "year": bson.M{"$year": "$timestamp"},
            "month": bson.M{"$month": "$timestamp"},
            "day": bson.M{"$dayOfMonth": "$timestamp"},
        }
    }

    pipeline := []bson.M{
        {"$match": bson.M{"timestamp": bson.M{"$gte": time.Now().AddDate(0, -1, 0)}}},
        {"$group": bson.M{
            "_id": groupBy,
            "avg": bson.M{"$avg": "$value"},
            "max": bson.M{"$max": "$value"},
            "min": bson.M{"$min": "$value"},
            "count": bson.M{"$sum": 1},
        }},
        {"$sort": bson.M{"_id": 1}},
    }

    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())

    var results []bson.M
    err = cursor.All(context.Background(), &results)
    return results, err
}
```

## 面试高频问题

### Q1: 聚合管道vs普通查询的选择？
- **简单查询**：使用`find()`，如单条件过滤、简单排序
- **复杂统计**：使用聚合管道，如分组统计、多表关联、复杂计算
- **性能考虑**：聚合管道在数据库层面处理，减少网络传输

```go
// 简单查询
cursor, err := collection.Find(ctx, bson.M{"status": "completed"})

// 复杂统计
pipeline := []bson.M{
    {"$match": bson.M{"status": "completed"}},
    {"$group": bson.M{"_id": "$user_id", "total": bson.M{"$sum": "$amount"}}},
}
cursor, err := collection.Aggregate(ctx, pipeline)
```

### Q2: 聚合管道的内存限制？
- **限制**：单个聚合阶段默认内存限制100MB
- **解决方案**：
  - 使用`allowDiskUse: true`允许使用磁盘
  - 尽早使用`$match`过滤数据
  - 使用`$project`减少字段
  - 分批处理大数据集

### Q3: 如何优化聚合管道性能？
1. **索引优化**：为`$match`、`$sort`、`$group`字段创建索引
2. **阶段顺序**：`$match` → `$project` → `$group` → `$sort` → `$limit`
3. **减少数据量**：尽早过滤和投影
4. **避免大数组**：谨慎使用`$push`操作

### Q4: $lookup的性能考虑？
- **索引**：确保关联字段有索引
- **数据量**：避免关联大集合
- **替代方案**：考虑数据冗余设计
- **pipeline优化**：在`$lookup`内部使用pipeline过滤

### Q5: 聚合管道的执行顺序优化？
MongoDB查询优化器会自动优化某些阶段顺序：
- `$match`会尽可能前移
- `$sort` + `$limit`会被优化为top-k排序
- `$project`可以减少后续阶段的数据量

## 最佳实践总结

### 性能优化要点
1. **索引设计**：为聚合操作创建合适的复合索引
2. **内存管理**：大数据集使用`allowDiskUse`
3. **阶段优化**：合理安排聚合阶段顺序
4. **数据过滤**：尽早使用`$match`减少数据量

### 常用优化技巧
- **提前过滤**：`$match`放在管道前面
- **字段投影**：`$project`只保留必要字段
- **索引利用**：确保`$match`和`$sort`能使用索引
- **批量处理**：大数据集分批处理避免内存溢出

### 监控和调试
- 使用`explain()`分析执行计划
- 启用profiling监控慢查询
- 记录聚合执行时间
- 监控内存使用情况
