# 常见后台安全问题与防护

## 身份认证与授权

### JWT安全实现
**核心要点**：
- 使用强密钥签名
- 设置合理的过期时间
- 实施Refresh Token机制
- 验证Token的完整性

```go
type Claims struct {
    UserID   int    `json:"user_id"`
    Username string `json:"username"`
    Role     string `json:"role"`
    jwt.RegisteredClaims
}

type JWTManager struct {
    secretKey   []byte
    tokenExpiry time.Duration
}

func (j *JWTManager) GenerateToken(userID int, username, role string) (string, error) {
    claims := &Claims{
        UserID:   userID,
        Username: username,
        Role:     role,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.tokenExpiry)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            Issuer:    "your-app",
        },
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString(j.secretKey)
}

func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        return j.secretKey, nil
    })

    if err != nil {
        return nil, err
    }

    if claims, ok := token.Claims.(*Claims); ok && token.Valid {
        return claims, nil
    }

    return nil, jwt.ErrInvalidKey
}
```

### RBAC权限控制
**设计原则**：
- 最小权限原则
- 角色分离
- 权限继承
- 动态权限检查

```go
type Permission string
type Role string

const (
    PermissionRead   Permission = "read"
    PermissionWrite  Permission = "write"
    PermissionDelete Permission = "delete"
)

type RBACManager struct {
    rolePermissions map[Role][]Permission
    userRoles       map[int][]Role
}

func (r *RBACManager) HasPermission(userID int, permission Permission) bool {
    roles := r.userRoles[userID]
    for _, role := range roles {
        permissions := r.rolePermissions[role]
        for _, p := range permissions {
            if p == permission {
                return true
            }
        }
    }
    return false
}

// 权限检查中间件
func (r *RBACManager) RequirePermission(permission Permission) func(http.Handler) http.Handler {
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
            userID := getUserIDFromContext(req.Context())
            if !r.HasPermission(userID, permission) {
                http.Error(w, "Forbidden", http.StatusForbidden)
                return
            }
            next.ServeHTTP(w, req)
        })
    }
}
```

## 会话管理安全

### 安全会话设计
**关键要素**：
- 安全的Session ID生成
- 合理的过期时间
- 及时清理过期会话
- 防止会话固定攻击

```go
type Session struct {
    ID        string
    UserID    int
    CreatedAt time.Time
    ExpiresAt time.Time
    Data      map[string]interface{}
}

type SessionManager struct {
    sessions map[string]*Session
    mutex    sync.RWMutex
    timeout  time.Duration
}

func (sm *SessionManager) CreateSession(userID int) (*Session, error) {
    sessionID, err := generateSessionID()
    if err != nil {
        return nil, err
    }

    session := &Session{
        ID:        sessionID,
        UserID:    userID,
        CreatedAt: time.Now(),
        ExpiresAt: time.Now().Add(sm.timeout),
        Data:      make(map[string]interface{}),
    }

    sm.mutex.Lock()
    sm.sessions[sessionID] = session
    sm.mutex.Unlock()

    return session, nil
}

func (sm *SessionManager) GetSession(sessionID string) (*Session, bool) {
    sm.mutex.RLock()
    defer sm.mutex.RUnlock()

    session, exists := sm.sessions[sessionID]
    if !exists || time.Now().After(session.ExpiresAt) {
        return nil, false
    }

    return session, true
}

func generateSessionID() (string, error) {
    bytes := make([]byte, 32)
    _, err := rand.Read(bytes)
    if err != nil {
        return "", err
    }
    return hex.EncodeToString(bytes), nil
}
```

## 密码安全

### 密码哈希最佳实践
**安全要求**：
- 使用强哈希算法（Argon2、bcrypt、scrypt）
- 加盐防止彩虹表攻击
- 设置合适的计算成本
- 使用常量时间比较

```go
type PasswordManager struct {
    memory      uint32 // 内存使用量
    iterations  uint32 // 迭代次数
    parallelism uint8  // 并行度
    saltLength  uint32 // 盐长度
    keyLength   uint32 // 密钥长度
}

func NewPasswordManager() *PasswordManager {
    return &PasswordManager{
        memory:      64 * 1024, // 64 MB
        iterations:  3,
        parallelism: 2,
        saltLength:  16,
        keyLength:   32,
    }
}

func (pm *PasswordManager) HashPassword(password string) (string, error) {
    salt, err := generateSalt(pm.saltLength)
    if err != nil {
        return "", err
    }

    hash := argon2.IDKey([]byte(password), salt, pm.iterations, pm.memory, pm.parallelism, pm.keyLength)

    b64Salt := base64.RawStdEncoding.EncodeToString(salt)
    b64Hash := base64.RawStdEncoding.EncodeToString(hash)

    encodedHash := fmt.Sprintf("$argon2id$v=%d$m=%d,t=%d,p=%d$%s$%s",
        argon2.Version, pm.memory, pm.iterations, pm.parallelism, b64Salt, b64Hash)

    return encodedHash, nil
}

func (pm *PasswordManager) VerifyPassword(password, encodedHash string) bool {
    // 解析存储的哈希
    parts := strings.Split(encodedHash, "$")
    if len(parts) != 6 {
        return false
    }

    // 提取参数和盐
    salt, _ := base64.RawStdEncoding.DecodeString(parts[4])
    hash, _ := base64.RawStdEncoding.DecodeString(parts[5])

    // 重新计算哈希
    otherHash := argon2.IDKey([]byte(password), salt, pm.iterations, pm.memory, pm.parallelism, uint32(len(hash)))

    // 常量时间比较
    return subtle.ConstantTimeCompare(hash, otherHash) == 1
}
```

## API安全防护

### 限流策略
**目的**：防止API滥用和DDoS攻击
**常用算法**：
- 令牌桶算法
- 漏桶算法
- 滑动窗口算法
- 固定窗口算法

```go
type RateLimiter struct {
    requests map[string][]time.Time
    mutex    sync.RWMutex
    limit    int           // 限制数量
    window   time.Duration // 时间窗口
}

func (rl *RateLimiter) Allow(clientID string) bool {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()

    now := time.Now()

    // 清理过期请求
    requests := rl.requests[clientID]
    validRequests := make([]time.Time, 0)

    for _, reqTime := range requests {
        if now.Sub(reqTime) < rl.window {
            validRequests = append(validRequests, reqTime)
        }
    }

    // 检查是否超过限制
    if len(validRequests) >= rl.limit {
        return false
    }

    // 添加新请求
    validRequests = append(validRequests, now)
    rl.requests[clientID] = validRequests

    return true
}

func (rl *RateLimiter) Middleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        clientID := getClientID(r) // 基于IP、用户ID或API密钥

        if !rl.Allow(clientID) {
            http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
            return
        }

        next.ServeHTTP(w, r)
    })
}
```

## 数据加密

### 加密策略
**传输加密**：HTTPS/TLS保护数据传输
**存储加密**：敏感数据加密存储
**应用层加密**：关键字段单独加密

### AES-GCM加密实现
**优势**：
- 提供机密性和完整性
- 认证加密模式
- 性能优秀

```go
type AESEncryption struct {
    key []byte
}

func (a *AESEncryption) Encrypt(plaintext string) (string, error) {
    block, err := aes.NewCipher(a.key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    // 生成随机nonce
    nonce := make([]byte, gcm.NonceSize())
    if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }

    // 加密并认证
    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (a *AESEncryption) Decrypt(ciphertext string) (string, error) {
    data, err := base64.StdEncoding.DecodeString(ciphertext)
    if err != nil {
        return "", err
    }

    block, err := aes.NewCipher(a.key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    nonceSize := gcm.NonceSize()
    if len(data) < nonceSize {
        return "", fmt.Errorf("ciphertext too short")
    }

    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return "", err
    }

    return string(plaintext), nil
}
```

## 常见安全威胁与防护

### 1. 注入攻击
**SQL注入防护**：
- 使用参数化查询
- 输入验证和过滤
- 最小权限数据库账户
- WAF防护

### 2. 跨站脚本攻击（XSS）
**防护措施**：
- 输出编码
- Content Security Policy (CSP)
- HttpOnly Cookie
- 输入验证

### 3. 跨站请求伪造（CSRF）
**防护方法**：
- CSRF Token验证
- SameSite Cookie属性
- 验证Referer头
- 双重提交Cookie

### 4. 会话劫持
**防护策略**：
- HTTPS传输
- 安全的Session ID
- 会话超时
- IP绑定验证

## 面试高频问题

### Q1: 如何设计安全的API认证系统？
1. **HTTPS传输**：保证传输安全
2. **JWT + Refresh Token**：无状态认证
3. **RBAC权限控制**：细粒度权限管理
4. **API限流**：防止滥用
5. **输入验证**：防止注入攻击

### Q2: 密码应该如何安全存储？
- **强哈希算法**：使用Argon2、bcrypt、scrypt
- **加盐处理**：防止彩虹表攻击
- **合适成本**：平衡安全性和性能
- **常量时间比较**：防止时序攻击

### Q3: 如何防止暴力破解攻击？
- **账户锁定**：连续失败后锁定账户
- **验证码**：人机验证
- **限流控制**：限制请求频率
- **异常检测**：监控异常登录行为

### Q4: 什么是零信任安全模型？
- **永不信任，始终验证**：不信任任何用户或设备
- **最小权限**：只授予必要的最小权限
- **持续验证**：动态评估访问权限
- **微分段**：网络和应用层分段

### Q5: 如何防护API安全？
- **认证授权**：强身份验证和权限控制
- **输入验证**：严格验证所有输入
- **输出编码**：防止XSS攻击
- **限流熔断**：防止API滥用
- **日志监控**：记录和监控API访问

## 安全最佳实践

### 设计原则
1. **深度防御**：多层安全防护
2. **最小权限**：只授予必要权限
3. **默认拒绝**：默认拒绝所有访问
4. **安全失败**：失败时保持安全状态

### 开发实践
- **安全编码**：遵循安全编码规范
- **代码审查**：定期进行安全代码审查
- **依赖管理**：及时更新安全补丁
- **安全测试**：集成安全测试到CI/CD

### 运维实践
- **监控告警**：实时安全监控
- **事件响应**：制定安全事件响应计划
- **定期审计**：定期安全审计和评估
- **员工培训**：提高安全意识
