# 分布式限流策略

## 限流算法

### 1. 令牌桶算法（Token Bucket）
**原理**：以固定速率向桶中放入令牌，请求需要获取令牌才能通过
**特点**：允许突发流量，平滑限流

```go
type TokenBucket struct {
    capacity    int64     // 桶容量
    tokens      int64     // 当前令牌数
    refillRate  int64     // 令牌补充速率（每秒）
    lastRefill  time.Time // 上次补充时间
    mutex       sync.Mutex
}

func (tb *TokenBucket) Allow() bool {
    tb.mutex.Lock()
    defer tb.mutex.Unlock()
    
    now := time.Now()
    // 计算需要补充的令牌数
    elapsed := now.Sub(tb.lastRefill).Seconds()
    tokensToAdd := int64(elapsed * float64(tb.refillRate))
    
    tb.tokens = min(tb.capacity, tb.tokens+tokensToAdd)
    tb.lastRefill = now
    
    if tb.tokens > 0 {
        tb.tokens--
        return true
    }
    return false
}
```

### 2. 漏桶算法（Leaky Bucket）
**原理**：请求进入漏桶，以固定速率流出
**特点**：严格控制流量速率，不允许突发

```go
type LeakyBucket struct {
    capacity   int64
    queue      []Request
    leakRate   int64 // 每秒处理请求数
    lastLeak   time.Time
    mutex      sync.Mutex
}

func (lb *LeakyBucket) Allow(req Request) bool {
    lb.mutex.Lock()
    defer lb.mutex.Unlock()
    
    // 先漏水
    lb.leak()
    
    // 检查是否还有空间
    if int64(len(lb.queue)) < lb.capacity {
        lb.queue = append(lb.queue, req)
        return true
    }
    return false
}

func (lb *LeakyBucket) leak() {
    now := time.Now()
    elapsed := now.Sub(lb.lastLeak).Seconds()
    leakCount := int64(elapsed * float64(lb.leakRate))
    
    if leakCount > 0 {
        if leakCount >= int64(len(lb.queue)) {
            lb.queue = lb.queue[:0]
        } else {
            lb.queue = lb.queue[leakCount:]
        }
        lb.lastLeak = now
    }
}
```

### 3. 滑动窗口算法
**原理**：统计滑动时间窗口内的请求数量
**特点**：精确控制时间窗口内的请求量

```go
type SlidingWindow struct {
    windowSize time.Duration
    limit      int64
    requests   []time.Time
    mutex      sync.Mutex
}

func (sw *SlidingWindow) Allow() bool {
    sw.mutex.Lock()
    defer sw.mutex.Unlock()
    
    now := time.Now()
    cutoff := now.Add(-sw.windowSize)
    
    // 清理过期请求
    validRequests := 0
    for _, reqTime := range sw.requests {
        if reqTime.After(cutoff) {
            sw.requests[validRequests] = reqTime
            validRequests++
        }
    }
    sw.requests = sw.requests[:validRequests]
    
    // 检查是否超限
    if int64(len(sw.requests)) < sw.limit {
        sw.requests = append(sw.requests, now)
        return true
    }
    return false
}
```

## 分布式限流实现

### 1. 基于Redis的分布式限流
```go
// Redis + Lua脚本实现原子性限流
const luaScript = `
local key = KEYS[1]
local limit = tonumber(ARGV[1])
local window = tonumber(ARGV[2])
local current_time = tonumber(ARGV[3])

-- 清理过期数据
redis.call('ZREMRANGEBYSCORE', key, 0, current_time - window)

-- 获取当前窗口内的请求数
local current_requests = redis.call('ZCARD', key)

if current_requests < limit then
    -- 添加当前请求
    redis.call('ZADD', key, current_time, current_time)
    redis.call('EXPIRE', key, window)
    return 1
else
    return 0
end
`

type RedisRateLimiter struct {
    client *redis.Client
    script *redis.Script
}

func (r *RedisRateLimiter) Allow(key string, limit int64, window time.Duration) bool {
    now := time.Now().UnixNano()
    windowNano := window.Nanoseconds()
    
    result, err := r.script.Run(context.Background(), r.client, 
        []string{key}, limit, windowNano, now).Result()
    
    if err != nil {
        return false
    }
    
    return result.(int64) == 1
}
```

### 2. 基于令牌桶的分布式限流
```go
type DistributedTokenBucket struct {
    redis      *redis.Client
    key        string
    capacity   int64
    refillRate int64
}

func (dtb *DistributedTokenBucket) Allow() bool {
    luaScript := `
    local key = KEYS[1]
    local capacity = tonumber(ARGV[1])
    local refill_rate = tonumber(ARGV[2])
    local now = tonumber(ARGV[3])
    
    local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
    local tokens = tonumber(bucket[1]) or capacity
    local last_refill = tonumber(bucket[2]) or now
    
    -- 计算需要补充的令牌
    local elapsed = (now - last_refill) / 1000
    local tokens_to_add = math.floor(elapsed * refill_rate)
    tokens = math.min(capacity, tokens + tokens_to_add)
    
    if tokens > 0 then
        tokens = tokens - 1
        redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', key, 3600)
        return 1
    else
        redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', key, 3600)
        return 0
    end
    `
    
    now := time.Now().UnixMilli()
    result, err := dtb.redis.Eval(context.Background(), luaScript,
        []string{dtb.key}, dtb.capacity, dtb.refillRate, now).Result()
    
    if err != nil {
        return false
    }
    
    return result.(int64) == 1
}
```

## 限流策略

### 1. 限流粒度
- **全局限流**：整个系统的总请求量
- **用户限流**：单个用户的请求量
- **接口限流**：特定API的请求量
- **IP限流**：单个IP的请求量

### 2. 限流层次
- **接入层限流**：Nginx、网关层
- **服务层限流**：应用内部限流
- **资源层限流**：数据库、缓存限流

### 3. 限流响应策略
- **拒绝服务**：直接返回错误
- **排队等待**：将请求放入队列
- **服务降级**：返回简化响应

## 面试高频问题

### Q1: 令牌桶和漏桶的区别？
**令牌桶**：
- 允许突发流量
- 令牌生成速率固定
- 适合允许短时间突发的场景

**漏桶**：
- 严格控制流量速率
- 请求处理速率固定
- 适合需要平滑流量的场景

### Q2: 如何实现分布式限流？
**方案**：
- Redis + Lua脚本保证原子性
- 使用滑动窗口或令牌桶算法
- 考虑Redis故障时的降级策略

### Q3: 限流算法的选择？
**场景考虑**：
- 是否允许突发流量
- 限流精度要求
- 系统性能要求
- 实现复杂度

### Q4: 分布式限流的挑战？
**挑战**：
- 多节点状态同步
- 网络延迟影响
- 单点故障问题
- 时钟同步问题

**解决方案**：
- 使用Redis集群
- 本地限流 + 全局限流结合
- 熔断降级机制
