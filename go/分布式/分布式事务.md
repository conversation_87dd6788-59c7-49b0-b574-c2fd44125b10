# 分布式事务

分布式事务是分布式系统中保证数据一致性的重要机制，主要解决跨多个数据库或服务的事务一致性问题。

## 分布式事务实现方式

### 1. **两阶段提交（2PC）**

**原理**：
- **准备阶段**：协调者询问所有参与者是否可以提交
- **提交阶段**：根据参与者响应决定提交或回滚

```go
// 2PC协调者实现
type TwoPhaseCoordinator struct {
    participants []Participant
}

func (c *TwoPhaseCoordinator) Commit(tx Transaction) error {
    // 阶段1：准备阶段
    for _, p := range c.participants {
        if err := p.Prepare(tx); err != nil {
            c.abort(tx) // 任一参与者失败则回滚
            return err
        }
    }

    // 阶段2：提交阶段
    for _, p := range c.participants {
        p.Commit(tx)
    }
    return nil
}
```

**特点**：
- **强一致性**：保证所有参与者状态一致
- **阻塞问题**：协调者故障时参与者会阻塞
- **适用场景**：对一致性要求极高的场景

### 2. **三阶段提交（3PC）**

**改进**：增加预提交阶段，减少阻塞时间
**阶段**：CanCommit → PreCommit → DoCommit
**优点**：降低阻塞风险
**缺点**：实现复杂，网络开销大

### 3. **Saga模式（补偿事务）**

**原理**：将长事务拆分为多个本地事务，失败时执行补偿操作

```go
// Saga事务管理器
type SagaManager struct {
    steps []SagaStep
}

type SagaStep struct {
    Action      func() error
    Compensate  func() error
}

func (s *SagaManager) Execute() error {
    executed := 0

    // 正向执行
    for i, step := range s.steps {
        if err := step.Action(); err != nil {
            // 执行补偿操作
            s.compensate(executed)
            return err
        }
        executed = i + 1
    }
    return nil
}

func (s *SagaManager) compensate(executed int) {
    // 逆序执行补偿操作
    for i := executed - 1; i >= 0; i-- {
        s.steps[i].Compensate()
    }
}
```

**特点**：
- **最终一致性**：允许中间状态不一致
- **高可用性**：无阻塞，适合长事务
- **实现复杂**：需要设计补偿逻辑

### 4. **TCC模式（Try-Confirm-Cancel）**

**原理**：三阶段操作模式
- **Try**：预留资源，执行业务检查
- **Confirm**：确认执行，提交资源
- **Cancel**：取消执行，释放资源

```go
// TCC接口定义
type TCCService interface {
    Try(ctx context.Context, req *TCCRequest) error
    Confirm(ctx context.Context, req *TCCRequest) error
    Cancel(ctx context.Context, req *TCCRequest) error
}

// 订单TCC实现
func (s *OrderService) Try(ctx context.Context, req *TCCRequest) error {
    // 预留库存，冻结资金
    return s.reserveResources(req.OrderID)
}

func (s *OrderService) Confirm(ctx context.Context, req *TCCRequest) error {
    // 确认订单，扣减库存
    return s.confirmOrder(req.OrderID)
}

func (s *OrderService) Cancel(ctx context.Context, req *TCCRequest) error {
    // 取消订单，释放资源
    return s.cancelOrder(req.OrderID)
}
```

### 5. **基于消息的最终一致性**

**原理**：通过消息队列实现异步事务处理

```go
// 事件驱动的分布式事务
type EventDrivenTransaction struct {
    eventBus EventBus
}

func (t *EventDrivenTransaction) ProcessOrder(order *Order) error {
    // 1. 创建订单
    if err := t.createOrder(order); err != nil {
        return err
    }

    // 2. 发布事件，异步处理
    events := []Event{
        &InventoryReservedEvent{OrderID: order.ID},
        &PaymentProcessedEvent{OrderID: order.ID},
        &NotificationSentEvent{OrderID: order.ID},
    }

    for _, event := range events {
        t.eventBus.Publish(event)
    }

    return nil
}
```

## 面试高频问题

### Q1: 2PC的问题和解决方案？
**问题**：
- 单点故障：协调者故障导致阻塞
- 同步阻塞：参与者等待协调者响应
- 数据不一致：网络分区可能导致部分提交

**解决方案**：
- 使用3PC减少阻塞
- 引入超时机制
- 采用Saga或TCC模式

### Q2: Saga和TCC的区别？
**Saga**：
- 基于补偿的最终一致性
- 实现相对简单
- 适合长事务场景

**TCC**：
- 基于预留资源的强一致性
- 实现复杂，需要三个接口
- 适合对一致性要求高的场景

### Q3: 如何选择分布式事务方案？
**考虑因素**：
- 一致性要求：强一致性选2PC/TCC，最终一致性选Saga
- 性能要求：高性能选异步方案
- 业务复杂度：简单业务选2PC，复杂业务选Saga
- 系统可用性：高可用选非阻塞方案

### Q4: 分布式事务的最佳实践？
**设计原则**：
- 尽量避免分布式事务
- 优先使用本地事务
- 采用异步消息保证最终一致性
- 设计幂等操作
- 实现补偿机制