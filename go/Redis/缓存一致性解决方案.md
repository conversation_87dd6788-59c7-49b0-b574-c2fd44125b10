# 缓存一致性解决方案

## 缓存一致性问题

### 问题描述
当数据库中的数据发生变化时，如何保证缓存中的数据也能及时更新，避免读取到过期数据。

### 一致性级别
1. **强一致性**：缓存和数据库数据实时一致
2. **弱一致性**：允许短时间内数据不一致
3. **最终一致性**：经过一段时间后数据最终一致

## 常见更新策略

### 1. Cache Aside（旁路缓存）

#### 读操作流程
```
1. 先查缓存
2. 缓存命中：直接返回
3. 缓存未命中：查数据库，写入缓存，返回数据
```

#### 写操作流程
```
1. 先更新数据库
2. 再删除缓存
```

#### 代码示例
```go
// 读操作
func GetUser(id int) (*User, error) {
    // 1. 查缓存
    user, err := redis.Get(fmt.Sprintf("user:%d", id))
    if err == nil {
        return user, nil
    }
    
    // 2. 查数据库
    user, err = db.GetUser(id)
    if err != nil {
        return nil, err
    }
    
    // 3. 写入缓存
    redis.Set(fmt.Sprintf("user:%d", id), user, time.Hour)
    return user, nil
}

// 写操作
func UpdateUser(user *User) error {
    // 1. 更新数据库
    err := db.UpdateUser(user)
    if err != nil {
        return err
    }
    
    // 2. 删除缓存
    redis.Del(fmt.Sprintf("user:%d", user.ID))
    return nil
}
```

#### 优缺点
**优点**：
- 实现简单
- 缓存只存储被访问的数据
- 缓存故障不影响数据库

**缺点**：
- 存在短暂的数据不一致
- 缓存失效时数据库压力大

### 2. Write Through（写穿透）

#### 工作原理
```
写操作：同时更新缓存和数据库
读操作：只读缓存
```

#### 代码示例
```go
func UpdateUser(user *User) error {
    // 1. 更新数据库
    err := db.UpdateUser(user)
    if err != nil {
        return err
    }
    
    // 2. 更新缓存
    err = redis.Set(fmt.Sprintf("user:%d", user.ID), user, time.Hour)
    if err != nil {
        // 记录日志，但不影响主流程
        log.Error("Failed to update cache", err)
    }
    return nil
}
```

#### 优缺点
**优点**：
- 数据一致性好
- 读操作简单快速

**缺点**：
- 写操作延迟高
- 缓存中可能存储不常用数据

### 3. Write Behind（写回）

#### 工作原理
```
写操作：只更新缓存，异步更新数据库
读操作：只读缓存
```

#### 实现方式
```go
type WriteBackCache struct {
    cache    map[string]*User
    dirty    map[string]bool
    mu       sync.RWMutex
    stopCh   chan struct{}
}

func (w *WriteBackCache) UpdateUser(user *User) {
    w.mu.Lock()
    defer w.mu.Unlock()
    
    key := fmt.Sprintf("user:%d", user.ID)
    w.cache[key] = user
    w.dirty[key] = true
}

func (w *WriteBackCache) syncToDB() {
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            w.flushDirtyData()
        case <-w.stopCh:
            return
        }
    }
}

func (w *WriteBackCache) flushDirtyData() {
    w.mu.Lock()
    defer w.mu.Unlock()
    
    for key, isDirty := range w.dirty {
        if isDirty {
            user := w.cache[key]
            if err := db.UpdateUser(user); err == nil {
                w.dirty[key] = false
            }
        }
    }
}
```

#### 优缺点
**优点**：
- 写操作延迟低
- 减少数据库写压力

**缺点**：
- 实现复杂
- 可能丢失数据
- 数据一致性差

## 高级解决方案

### 1. 延时双删策略

#### 基本思路
```
1. 删除缓存
2. 更新数据库
3. 延时删除缓存（防止并发读写导致的脏数据）
```

#### 代码实现
```go
func UpdateUserWithDelayDelete(user *User) error {
    // 1. 删除缓存
    key := fmt.Sprintf("user:%d", user.ID)
    redis.Del(key)
    
    // 2. 更新数据库
    err := db.UpdateUser(user)
    if err != nil {
        return err
    }
    
    // 3. 延时删除缓存
    go func() {
        time.Sleep(500 * time.Millisecond) // 延时时间根据业务调整
        redis.Del(key)
    }()
    
    return nil
}
```

#### 延时时间确定
- 读数据库 + 写缓存的时间
- 一般设置为100-500ms

### 2. 基于消息队列的异步更新

#### 架构设计
```
应用 -> 数据库
    -> 消息队列 -> 缓存更新服务 -> 缓存
```

#### 实现示例
```go
// 发布更新事件
func UpdateUserWithMQ(user *User) error {
    // 1. 更新数据库
    err := db.UpdateUser(user)
    if err != nil {
        return err
    }
    
    // 2. 发送消息
    event := UserUpdateEvent{
        UserID: user.ID,
        Action: "update",
        Data:   user,
    }
    return mq.Publish("user.update", event)
}

// 消费更新事件
func handleUserUpdate(event UserUpdateEvent) {
    switch event.Action {
    case "update":
        key := fmt.Sprintf("user:%d", event.UserID)
        redis.Set(key, event.Data, time.Hour)
    case "delete":
        key := fmt.Sprintf("user:%d", event.UserID)
        redis.Del(key)
    }
}
```

### 3. 基于数据库变更日志（CDC）

#### 工作原理
```
数据库变更 -> Binlog -> CDC工具 -> 缓存更新
```

#### 常用工具
- **Canal**：阿里开源的MySQL binlog解析工具
- **Debezium**：红帽开源的CDC平台
- **Maxwell**：Zendesk开源的MySQL binlog解析工具

#### Canal示例
```go
// Canal客户端监听binlog
func listenBinlog() {
    connector := canal.NewSimpleCanalConnector(
        "127.0.0.1:11111", "", "", "example")
    
    connector.Connect()
    connector.Subscribe("test\\.user")
    
    for {
        message := connector.Get(100)
        for _, entry := range message.Entries {
            if entry.EntryType == protocol.EntryType_ROWDATA {
                handleRowChange(entry)
            }
        }
        connector.Ack(message.Id)
    }
}

func handleRowChange(entry *protocol.Entry) {
    rowChange := &protocol.RowChange{}
    proto.Unmarshal(entry.StoreValue, rowChange)
    
    for _, rowData := range rowChange.RowDatas {
        switch rowChange.EventType {
        case protocol.EventType_UPDATE:
            updateCache(rowData.AfterColumns)
        case protocol.EventType_DELETE:
            deleteCache(rowData.BeforeColumns)
        }
    }
}
```

## 分布式缓存一致性

### 1. 分布式锁

#### Redis分布式锁
```go
func UpdateUserWithLock(user *User) error {
    lockKey := fmt.Sprintf("lock:user:%d", user.ID)
    
    // 获取分布式锁
    lock, err := redis.SetNX(lockKey, "1", 10*time.Second)
    if err != nil || !lock {
        return errors.New("failed to acquire lock")
    }
    defer redis.Del(lockKey)
    
    // 执行更新操作
    return updateUserSafely(user)
}
```

### 2. 版本号机制

#### 乐观锁实现
```go
type UserWithVersion struct {
    User
    Version int64 `json:"version"`
}

func UpdateUserWithVersion(user *UserWithVersion) error {
    // 1. 查询当前版本
    current, err := db.GetUserWithVersion(user.ID)
    if err != nil {
        return err
    }
    
    // 2. 检查版本号
    if user.Version != current.Version {
        return errors.New("version conflict")
    }
    
    // 3. 更新数据（版本号+1）
    user.Version++
    err = db.UpdateUserWithVersion(user)
    if err != nil {
        return err
    }
    
    // 4. 更新缓存
    key := fmt.Sprintf("user:%d", user.ID)
    redis.Set(key, user, time.Hour)
    return nil
}
```

## 面试常见问题

### Q1: 为什么删除缓存而不是更新缓存？
**答案**：
1. **并发安全**：删除操作是幂等的
2. **性能考虑**：避免无效的缓存更新
3. **复杂度**：删除比更新简单

### Q2: 延时双删的延时时间如何确定？
**答案**：
延时时间应该大于一次读操作的时间，包括：
- 读数据库的时间
- 写缓存的时间
- 网络传输时间

### Q3: 如何处理缓存更新失败？
**答案**：
1. **重试机制**：指数退避重试
2. **消息队列**：异步重试
3. **监控告警**：及时发现问题
4. **降级策略**：直接查数据库

### Q4: 强一致性如何实现？
**答案**：
1. **分布式锁**：保证操作原子性
2. **两阶段提交**：保证事务一致性
3. **牺牲性能**：同步更新缓存和数据库

## 最佳实践

### 1. 选择合适的策略
- **读多写少**：Cache Aside
- **写多读少**：Write Through
- **高性能要求**：Write Behind + 异步同步

### 2. 监控和告警
- **缓存命中率**：监控缓存效果
- **数据一致性**：定期校验
- **更新延迟**：监控同步时间

### 3. 降级方案
- **缓存故障**：直接查数据库
- **同步失败**：记录日志，人工处理
- **性能问题**：临时关闭缓存

## 总结

缓存一致性是分布式系统中的经典问题：
- **没有银弹**：需要根据业务特点选择合适方案
- **权衡取舍**：在一致性、性能、复杂度间平衡
- **监控重要**：建立完善的监控和告警机制
- **降级准备**：准备好各种异常情况的处理方案
