# 消息推送系统设计

## 需求分析

### 功能需求
- **多端推送**：支持iOS、Android、Web、小程序
- **消息类型**：文本、图片、链接、富媒体消息
- **推送方式**：单播、组播、广播
- **定时推送**：支持定时和延时推送
- **消息统计**：送达率、点击率、转化率统计

### 非功能需求
- **高并发**：支持千万级用户同时在线
- **高可用**：99.9%服务可用性
- **低延迟**：消息推送延迟<3秒
- **高吞吐**：每秒处理10万条消息

## 系统架构

### 整体架构
```
业务系统 → API网关 → 推送服务 → 消息队列 → 推送网关 → 第三方推送平台
                                    ↓
                              用户连接管理 → WebSocket长连接
                                    ↓
                              统计服务 → 数据存储
```

### 核心组件

#### 1. 推送服务
```go
type PushService struct {
    messageQueue MessageQueue
    userManager  UserManager
    pushGateway  PushGateway
}

type PushMessage struct {
    ID          string            `json:"id"`
    Type        string            `json:"type"`        // single/group/broadcast
    UserIDs     []string          `json:"user_ids"`
    Title       string            `json:"title"`
    Content     string            `json:"content"`
    Payload     map[string]string `json:"payload"`
    ScheduleTime *time.Time       `json:"schedule_time"`
    ExpireTime   *time.Time       `json:"expire_time"`
}

func (s *PushService) SendMessage(msg *PushMessage) error {
    // 1. 消息验证
    if err := s.validateMessage(msg); err != nil {
        return err
    }
    
    // 2. 用户过滤
    validUsers, err := s.filterUsers(msg.UserIDs)
    if err != nil {
        return err
    }
    
    // 3. 消息分发
    for _, userID := range validUsers {
        pushTask := &PushTask{
            MessageID: msg.ID,
            UserID:    userID,
            Message:   msg,
        }
        s.messageQueue.Publish("push_queue", pushTask)
    }
    
    return nil
}
```

#### 2. 用户连接管理
```go
type ConnectionManager struct {
    connections map[string]*websocket.Conn
    userSessions map[string][]string  // userID -> sessionIDs
    mu          sync.RWMutex
}

func (cm *ConnectionManager) AddConnection(userID, sessionID string, conn *websocket.Conn) {
    cm.mu.Lock()
    defer cm.mu.Unlock()
    
    cm.connections[sessionID] = conn
    cm.userSessions[userID] = append(cm.userSessions[userID], sessionID)
}

func (cm *ConnectionManager) SendToUser(userID string, message []byte) error {
    cm.mu.RLock()
    sessions := cm.userSessions[userID]
    cm.mu.RUnlock()
    
    var errors []error
    for _, sessionID := range sessions {
        if conn, exists := cm.connections[sessionID]; exists {
            if err := conn.WriteMessage(websocket.TextMessage, message); err != nil {
                errors = append(errors, err)
                // 连接失效，清理连接
                cm.removeConnection(sessionID)
            }
        }
    }
    
    if len(errors) > 0 {
        return fmt.Errorf("failed to send to some sessions: %v", errors)
    }
    return nil
}
```

#### 3. 推送网关
```go
type PushGateway struct {
    apnsClient   *apns.Client     // iOS推送
    fcmClient    *fcm.Client      // Android推送
    connManager  *ConnectionManager // WebSocket推送
}

func (pg *PushGateway) Push(task *PushTask) error {
    user, err := pg.getUserInfo(task.UserID)
    if err != nil {
        return err
    }
    
    // 根据用户设备类型选择推送方式
    switch user.DeviceType {
    case "ios":
        return pg.pushToAPNS(user.DeviceToken, task.Message)
    case "android":
        return pg.pushToFCM(user.DeviceToken, task.Message)
    case "web":
        return pg.pushToWebSocket(user.UserID, task.Message)
    default:
        return fmt.Errorf("unsupported device type: %s", user.DeviceType)
    }
}

func (pg *PushGateway) pushToAPNS(deviceToken string, msg *PushMessage) error {
    notification := &apns.Notification{
        DeviceToken: deviceToken,
        Payload: &apns.Payload{
            Alert: &apns.Alert{
                Title: msg.Title,
                Body:  msg.Content,
            },
            Badge: 1,
            Sound: "default",
        },
    }
    
    _, err := pg.apnsClient.Push(notification)
    return err
}
```

## 数据存储设计

### 数据库表结构
```sql
-- 用户设备表
CREATE TABLE user_devices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL,
    device_type ENUM('ios', 'android', 'web') NOT NULL,
    device_token VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);

-- 消息记录表
CREATE TABLE push_messages (
    id VARCHAR(64) PRIMARY KEY,
    type ENUM('single', 'group', 'broadcast') NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    payload JSON,
    schedule_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'sending', 'sent', 'failed') DEFAULT 'pending'
);

-- 推送记录表
CREATE TABLE push_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    device_type VARCHAR(20),
    status ENUM('pending', 'sent', 'delivered', 'clicked', 'failed') DEFAULT 'pending',
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    clicked_at TIMESTAMP,
    error_msg TEXT,
    INDEX idx_message_id (message_id),
    INDEX idx_user_id (user_id)
);
```

## 系统优化

### 性能优化
1. **消息队列**：使用Kafka处理高并发推送
2. **批量处理**：批量获取用户信息和设备token
3. **连接池**：复用HTTP连接减少开销
4. **异步处理**：推送和统计异步进行

```go
type BatchPushProcessor struct {
    batchSize   int
    pushGateway *PushGateway
}

func (bp *BatchPushProcessor) ProcessBatch(tasks []*PushTask) {
    // 按设备类型分组
    groups := make(map[string][]*PushTask)
    for _, task := range tasks {
        deviceType := task.User.DeviceType
        groups[deviceType] = append(groups[deviceType], task)
    }
    
    // 并发处理不同设备类型
    var wg sync.WaitGroup
    for deviceType, groupTasks := range groups {
        wg.Add(1)
        go func(dt string, tasks []*PushTask) {
            defer wg.Done()
            bp.processByDeviceType(dt, tasks)
        }(deviceType, groupTasks)
    }
    wg.Wait()
}
```

### 高可用设计
1. **服务集群**：多实例部署，负载均衡
2. **消息持久化**：确保消息不丢失
3. **重试机制**：失败消息自动重试
4. **降级策略**：高峰期优先推送重要消息

### 监控告警
```go
type PushMetrics struct {
    TotalSent     int64 `json:"total_sent"`
    TotalDelivered int64 `json:"total_delivered"`
    TotalClicked   int64 `json:"total_clicked"`
    FailureRate    float64 `json:"failure_rate"`
}

func (s *StatService) GetPushMetrics(messageID string) (*PushMetrics, error) {
    var metrics PushMetrics
    
    // 统计发送数量
    s.db.QueryRow("SELECT COUNT(*) FROM push_records WHERE message_id = ?", messageID).Scan(&metrics.TotalSent)
    
    // 统计送达数量
    s.db.QueryRow("SELECT COUNT(*) FROM push_records WHERE message_id = ? AND status IN ('delivered', 'clicked')", messageID).Scan(&metrics.TotalDelivered)
    
    // 统计点击数量
    s.db.QueryRow("SELECT COUNT(*) FROM push_records WHERE message_id = ? AND status = 'clicked'", messageID).Scan(&metrics.TotalClicked)
    
    // 计算失败率
    if metrics.TotalSent > 0 {
        failedCount := metrics.TotalSent - metrics.TotalDelivered
        metrics.FailureRate = float64(failedCount) / float64(metrics.TotalSent)
    }
    
    return &metrics, nil
}
```

## 面试要点

### Q1: 如何保证消息不丢失？
- **消息持久化**：存储到数据库和消息队列
- **确认机制**：推送成功后更新状态
- **重试机制**：失败消息自动重试
- **死信队列**：处理最终失败的消息

### Q2: 如何处理大量离线用户？
- **离线存储**：消息暂存，用户上线时推送
- **消息合并**：相同类型消息合并推送
- **优先级队列**：重要消息优先推送
- **过期清理**：定期清理过期消息

### Q3: 如何提高推送到达率？
- **多通道推送**：同时使用多个推送平台
- **智能重试**：根据失败原因调整重试策略
- **用户画像**：根据用户活跃时间推送
- **A/B测试**：优化推送内容和时机

### Q4: 如何防止推送被当作垃圾消息？
- **用户偏好**：允许用户设置推送偏好
- **频率控制**：限制推送频率
- **内容质量**：提高推送内容相关性
- **时间控制**：避免在用户休息时间推送

## 总结

消息推送系统的关键技术点：
1. **多端适配**：支持不同平台的推送协议
2. **高并发处理**：消息队列+批量处理
3. **连接管理**：WebSocket长连接维护
4. **可靠性保证**：消息持久化+重试机制
5. **性能监控**：推送效果实时统计分析
