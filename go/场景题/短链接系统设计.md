# 短链接系统设计

## 需求分析

### 功能需求
- **短链生成**：将长URL转换为短URL
- **短链跳转**：通过短URL跳转到原始URL
- **自定义短链**：支持用户自定义短链后缀
- **链接统计**：点击次数、访问来源等统计信息
- **过期时间**：支持设置链接过期时间

### 非功能需求
- **高可用**：99.9%以上可用性
- **高性能**：跳转延迟<100ms
- **高并发**：支持10万QPS读取，1万QPS写入
- **数据一致性**：短链与长链映射关系准确

## 系统设计

### 架构概览
```
客户端 → 负载均衡 → API网关 → 短链服务 → 缓存/数据库
                              ↓
                           统计服务 → 消息队列 → 数据分析
```

### 核心算法

#### 短链生成策略
1. **Base62编码**：使用0-9,a-z,A-Z共62个字符
2. **自增ID**：数据库自增ID转Base62
3. **哈希算法**：MD5/SHA256后截取
4. **雪花算法**：分布式唯一ID生成

```go
// Base62编码实现
const base62Chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func encodeBase62(num uint64) string {
    if num == 0 {
        return "0"
    }
    
    var result []byte
    for num > 0 {
        result = append([]byte{base62Chars[num%62]}, result...)
        num /= 62
    }
    return string(result)
}

func decodeBase62(str string) uint64 {
    var result uint64
    for _, char := range str {
        result = result*62 + uint64(strings.IndexByte(base62Chars, byte(char)))
    }
    return result
}
```

### 数据存储设计

#### 数据库表结构
```sql
-- 短链映射表
CREATE TABLE short_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    short_code VARCHAR(10) UNIQUE NOT NULL,
    original_url TEXT NOT NULL,
    user_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_short_code (short_code),
    INDEX idx_user_id (user_id)
);

-- 访问统计表
CREATE TABLE url_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    short_code VARCHAR(10) NOT NULL,
    click_count BIGINT DEFAULT 0,
    last_accessed TIMESTAMP,
    INDEX idx_short_code (short_code)
);
```

#### 缓存策略
- **Redis缓存**：短链→长链映射，TTL设置
- **本地缓存**：热点数据LRU缓存
- **缓存更新**：写入时同步更新缓存

```go
type URLService struct {
    db    *sql.DB
    redis *redis.Client
    cache *lru.Cache
}

func (s *URLService) GetOriginalURL(shortCode string) (string, error) {
    // 1. 本地缓存查询
    if url, ok := s.cache.Get(shortCode); ok {
        return url.(string), nil
    }
    
    // 2. Redis缓存查询
    if url, err := s.redis.Get(shortCode).Result(); err == nil {
        s.cache.Add(shortCode, url)
        return url, nil
    }
    
    // 3. 数据库查询
    var originalURL string
    err := s.db.QueryRow("SELECT original_url FROM short_urls WHERE short_code = ? AND is_active = 1", shortCode).Scan(&originalURL)
    if err != nil {
        return "", err
    }
    
    // 更新缓存
    s.redis.Set(shortCode, originalURL, time.Hour)
    s.cache.Add(shortCode, originalURL)
    
    return originalURL, nil
}
```

## 系统优化

### 性能优化
1. **读写分离**：主库写入，从库读取
2. **分库分表**：按短链前缀分片
3. **CDN加速**：静态资源和跳转页面缓存
4. **异步处理**：统计数据异步更新

### 高可用设计
1. **多机房部署**：跨地域容灾
2. **服务降级**：缓存失效时返回默认页面
3. **熔断机制**：防止雪崩效应
4. **监控告警**：实时监控系统状态

### 安全考虑
1. **恶意链接检测**：黑名单过滤
2. **访问频率限制**：防止恶意刷量
3. **链接有效性**：定期检查原始链接
4. **用户权限**：登录用户才能创建

## 扩展功能

### 统计分析
```go
type ClickEvent struct {
    ShortCode string    `json:"short_code"`
    IP        string    `json:"ip"`
    UserAgent string    `json:"user_agent"`
    Referer   string    `json:"referer"`
    Timestamp time.Time `json:"timestamp"`
}

func (s *URLService) RecordClick(shortCode, ip, userAgent, referer string) {
    event := ClickEvent{
        ShortCode: shortCode,
        IP:        ip,
        UserAgent: userAgent,
        Referer:   referer,
        Timestamp: time.Now(),
    }
    
    // 异步发送到消息队列
    s.messageQueue.Publish("click_events", event)
}
```

### 批量操作
- **批量生成**：一次生成多个短链
- **批量查询**：减少数据库查询次数
- **批量统计**：定时批量更新统计数据

## 面试要点

### Q1: 如何保证短链的唯一性？
- **数据库唯一索引**：short_code字段唯一约束
- **分布式锁**：生成时加锁防止重复
- **重试机制**：冲突时重新生成

### Q2: 如何处理热点数据？
- **多级缓存**：本地缓存+Redis缓存
- **缓存预热**：提前加载热点数据
- **读写分离**：减轻主库压力

### Q3: 如何估算存储容量？
- **短链数量**：每天100万个，保存5年 = 18亿条记录
- **单条记录**：约200字节
- **总存储量**：18亿 × 200字节 ≈ 360GB

### Q4: 如何实现高可用？
- **服务集群**：多实例部署
- **数据备份**：主从复制+定期备份
- **故障转移**：自动切换到备用服务
- **降级策略**：核心功能优先保证

## 总结

短链接系统的核心挑战：
1. **唯一性保证**：避免短链冲突
2. **高性能读取**：毫秒级跳转响应
3. **数据一致性**：缓存与数据库同步
4. **系统扩展性**：支持海量数据和高并发
5. **安全防护**：防止恶意链接和攻击
