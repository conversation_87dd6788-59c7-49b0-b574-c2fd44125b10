# 搜索引擎设计

## 需求分析

### 功能需求
- **网页抓取**：爬取互联网网页内容
- **内容索引**：建立倒排索引支持快速检索
- **搜索查询**：支持关键词、短语、布尔查询
- **结果排序**：按相关性对搜索结果排序
- **搜索建议**：自动补全和搜索建议

### 非功能需求
- **海量数据**：支持百亿级网页索引
- **高并发**：支持10万QPS查询
- **低延迟**：搜索响应时间<200ms
- **高可用**：99.9%服务可用性

## 系统架构

### 整体架构
```
网页爬虫 → 内容处理 → 索引构建 → 索引存储
                                    ↓
用户查询 → 查询处理 → 索引检索 → 结果排序 → 返回结果
```

### 核心组件

#### 1. 网页爬虫
```go
type WebCrawler struct {
    urlQueue    chan string
    httpClient  *http.Client
    robotsCache map[string]*robotstxt.RobotsData
    visited     sync.Map
}

type CrawlResult struct {
    URL         string    `json:"url"`
    Title       string    `json:"title"`
    Content     string    `json:"content"`
    Links       []string  `json:"links"`
    CrawledAt   time.Time `json:"crawled_at"`
}

func (c *WebCrawler) CrawlPage(url string) (*CrawlResult, error) {
    // 1. 检查robots.txt
    if !c.isAllowedByCrawl(url) {
        return nil, fmt.Errorf("blocked by robots.txt")
    }
    
    // 2. 检查是否已访问
    if _, visited := c.visited.Load(url); visited {
        return nil, fmt.Errorf("already visited")
    }
    
    // 3. 发起HTTP请求
    resp, err := c.httpClient.Get(url)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    // 4. 解析HTML内容
    doc, err := goquery.NewDocumentFromReader(resp.Body)
    if err != nil {
        return nil, err
    }
    
    result := &CrawlResult{
        URL:       url,
        Title:     doc.Find("title").Text(),
        Content:   c.extractText(doc),
        Links:     c.extractLinks(doc, url),
        CrawledAt: time.Now(),
    }
    
    // 5. 标记为已访问
    c.visited.Store(url, true)
    
    return result, nil
}
```

#### 2. 内容处理与索引构建
```go
type IndexBuilder struct {
    tokenizer    Tokenizer
    indexWriter  IndexWriter
}

type Document struct {
    ID       string            `json:"id"`
    URL      string            `json:"url"`
    Title    string            `json:"title"`
    Content  string            `json:"content"`
    Metadata map[string]string `json:"metadata"`
}

type InvertedIndex struct {
    Term      string             `json:"term"`
    Documents []DocumentPosting  `json:"documents"`
}

type DocumentPosting struct {
    DocID     string  `json:"doc_id"`
    Frequency int     `json:"frequency"`
    Positions []int   `json:"positions"`
    Score     float64 `json:"score"`
}

func (ib *IndexBuilder) BuildIndex(doc *Document) error {
    // 1. 文本预处理
    tokens := ib.tokenizer.Tokenize(doc.Title + " " + doc.Content)
    
    // 2. 计算词频
    termFreq := make(map[string]int)
    termPositions := make(map[string][]int)
    
    for pos, token := range tokens {
        termFreq[token]++
        termPositions[token] = append(termPositions[token], pos)
    }
    
    // 3. 构建倒排索引
    for term, freq := range termFreq {
        posting := DocumentPosting{
            DocID:     doc.ID,
            Frequency: freq,
            Positions: termPositions[term],
            Score:     ib.calculateTFIDF(term, freq, len(tokens)),
        }
        
        if err := ib.indexWriter.AddPosting(term, posting); err != nil {
            return err
        }
    }
    
    return nil
}

func (ib *IndexBuilder) calculateTFIDF(term string, tf, docLength int) float64 {
    // TF = 词频 / 文档总词数
    termFreq := float64(tf) / float64(docLength)
    
    // IDF = log(总文档数 / 包含该词的文档数)
    totalDocs := ib.getTotalDocuments()
    docsWithTerm := ib.getDocumentsWithTerm(term)
    idf := math.Log(float64(totalDocs) / float64(docsWithTerm))
    
    return termFreq * idf
}
```

#### 3. 查询处理与检索
```go
type SearchEngine struct {
    indexReader IndexReader
    ranker      ResultRanker
}

type SearchQuery struct {
    Query     string   `json:"query"`
    Filters   []Filter `json:"filters"`
    Page      int      `json:"page"`
    PageSize  int      `json:"page_size"`
}

type SearchResult struct {
    Documents []DocumentResult `json:"documents"`
    Total     int              `json:"total"`
    TimeTaken time.Duration    `json:"time_taken"`
}

type DocumentResult struct {
    ID       string  `json:"id"`
    URL      string  `json:"url"`
    Title    string  `json:"title"`
    Snippet  string  `json:"snippet"`
    Score    float64 `json:"score"`
}

func (se *SearchEngine) Search(query *SearchQuery) (*SearchResult, error) {
    startTime := time.Now()
    
    // 1. 查询预处理
    terms := se.preprocessQuery(query.Query)
    
    // 2. 检索候选文档
    candidates, err := se.retrieveCandidates(terms)
    if err != nil {
        return nil, err
    }
    
    // 3. 结果排序
    rankedResults := se.ranker.Rank(candidates, terms)
    
    // 4. 分页处理
    start := (query.Page - 1) * query.PageSize
    end := start + query.PageSize
    if end > len(rankedResults) {
        end = len(rankedResults)
    }
    
    result := &SearchResult{
        Documents: rankedResults[start:end],
        Total:     len(rankedResults),
        TimeTaken: time.Since(startTime),
    }
    
    return result, nil
}

func (se *SearchEngine) retrieveCandidates(terms []string) ([]DocumentResult, error) {
    var allCandidates []DocumentResult
    
    for _, term := range terms {
        // 从倒排索引中获取包含该词的文档
        postings, err := se.indexReader.GetPostings(term)
        if err != nil {
            continue
        }
        
        for _, posting := range postings {
            doc, err := se.indexReader.GetDocument(posting.DocID)
            if err != nil {
                continue
            }
            
            candidate := DocumentResult{
                ID:      doc.ID,
                URL:     doc.URL,
                Title:   doc.Title,
                Snippet: se.generateSnippet(doc.Content, terms),
                Score:   posting.Score,
            }
            
            allCandidates = append(allCandidates, candidate)
        }
    }
    
    return allCandidates, nil
}
```

#### 4. 结果排序算法
```go
type ResultRanker struct {
    pageRankScores map[string]float64
}

func (rr *ResultRanker) Rank(candidates []DocumentResult, queryTerms []string) []DocumentResult {
    // 1. 计算相关性得分
    for i := range candidates {
        candidates[i].Score = rr.calculateRelevanceScore(&candidates[i], queryTerms)
    }
    
    // 2. 按得分排序
    sort.Slice(candidates, func(i, j int) bool {
        return candidates[i].Score > candidates[j].Score
    })
    
    return candidates
}

func (rr *ResultRanker) calculateRelevanceScore(doc *DocumentResult, queryTerms []string) float64 {
    var score float64
    
    // TF-IDF得分
    score += doc.Score
    
    // PageRank得分
    if pageRank, exists := rr.pageRankScores[doc.ID]; exists {
        score += pageRank * 0.3
    }
    
    // 标题匹配加权
    titleScore := rr.calculateTitleMatch(doc.Title, queryTerms)
    score += titleScore * 0.5
    
    // URL权重（域名权威性）
    urlScore := rr.calculateURLScore(doc.URL)
    score += urlScore * 0.2
    
    return score
}
```

## 数据存储设计

### 索引存储
```go
// 使用分片存储大规模索引
type ShardedIndex struct {
    shards []IndexShard
}

type IndexShard struct {
    id          int
    termIndex   map[string][]DocumentPosting  // 倒排索引
    docStore    map[string]Document           // 文档存储
    mu          sync.RWMutex
}

func (si *ShardedIndex) GetShard(term string) *IndexShard {
    hash := fnv.New32a()
    hash.Write([]byte(term))
    shardID := int(hash.Sum32()) % len(si.shards)
    return &si.shards[shardID]
}
```

### 缓存策略
```go
type SearchCache struct {
    queryCache   *lru.Cache  // 查询结果缓存
    indexCache   *lru.Cache  // 索引片段缓存
    redis        *redis.Client
}

func (sc *SearchCache) GetSearchResult(query string) (*SearchResult, bool) {
    // 1. 本地缓存查询
    if result, ok := sc.queryCache.Get(query); ok {
        return result.(*SearchResult), true
    }
    
    // 2. Redis缓存查询
    if data, err := sc.redis.Get("search:" + query).Result(); err == nil {
        var result SearchResult
        if json.Unmarshal([]byte(data), &result) == nil {
            sc.queryCache.Add(query, &result)
            return &result, true
        }
    }
    
    return nil, false
}
```

## 系统优化

### 性能优化
1. **分布式索引**：按词汇或文档分片
2. **并行检索**：多个分片并行查询
3. **结果缓存**：热门查询结果缓存
4. **预计算**：PageRank等指标预计算

### 扩展性设计
1. **水平扩展**：增加索引分片和查询节点
2. **负载均衡**：查询请求均匀分发
3. **数据分区**：按时间或主题分区
4. **增量更新**：支持索引增量更新

## 面试要点

### Q1: 如何处理海量网页数据？
- **分布式爬虫**：多机器并行爬取
- **增量爬取**：只爬取更新的页面
- **优先级队列**：重要页面优先爬取
- **去重机制**：避免重复爬取

### Q2: 如何提高搜索速度？
- **倒排索引**：快速定位包含关键词的文档
- **索引分片**：并行查询多个分片
- **缓存机制**：热门查询结果缓存
- **预处理**：查询预处理和优化

### Q3: 如何保证搜索结果质量？
- **相关性算法**：TF-IDF + PageRank
- **反作弊**：检测和过滤垃圾页面
- **用户反馈**：根据点击率调整排序
- **人工审核**：重要查询人工优化

### Q4: 如何实现实时搜索？
- **增量索引**：新文档实时加入索引
- **索引合并**：定期合并小索引文件
- **热更新**：不停机更新索引
- **版本控制**：索引版本管理

## 总结

搜索引擎的核心技术：
1. **网页爬取**：分布式爬虫系统
2. **内容索引**：倒排索引构建
3. **查询处理**：查询解析和优化
4. **结果排序**：多因子排序算法
5. **系统架构**：分布式、高可用设计
