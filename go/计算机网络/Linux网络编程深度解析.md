# Linux网络编程深度解析

## Socket编程基础

### Socket类型
- **TCP Socket (SOCK_STREAM)**：面向连接的可靠传输
- **UDP Socket (SOCK_DGRAM)**：无连接的数据报传输
- **Unix域套接字**：本地进程间通信
- **原始套接字**：直接访问网络层协议

### 地址结构
- **sockaddr_in**：IPv4地址结构，包含IP地址和端口
- **sockaddr_in6**：IPv6地址结构
- **sockaddr_un**：Unix域套接字路径

### TCP编程流程
**服务器端**：socket() → bind() → listen() → accept() → read/write() → close()
**客户端**：socket() → connect() → read/write() → close()

## I/O多路复用

### 1. select模型
**特点**：
- 监控多个文件描述符的状态变化
- 使用fd_set位图表示文件描述符集合
- 有最大文件描述符数量限制（通常1024）
- 每次调用需要重新设置监控集合

**适用场景**：连接数较少的服务器

### 2. poll模型
**特点**：
- 使用pollfd结构数组管理文件描述符
- 没有最大文件描述符数量限制
- 不需要重新设置监控集合
- 返回就绪的文件描述符数量

**适用场景**：中等规模的并发连接

### 3. epoll模型
**特点**：
- 基于事件驱动，效率最高
- 支持水平触发（LT）和边缘触发（ET）
- 没有文件描述符数量限制
- 只返回就绪的文件描述符

**适用场景**：大规模并发连接的服务器

### epoll工作模式
- **LT模式（水平触发）**：只要有数据就会通知
- **ET模式（边缘触发）**：只在状态变化时通知，需要一次性读完所有数据

## 高频面试问题

### Q1: select、poll、epoll的区别？

| 特性 | select | poll | epoll |
|------|--------|------|-------|
| 最大连接数 | 1024（可修改） | 无限制 | 无限制 |
| 数据结构 | 位图 | 数组 | 红黑树+链表 |
| 时间复杂度 | O(n) | O(n) | O(1) |
| 内存拷贝 | 每次都要 | 每次都要 | 不需要 |
| 工作模式 | LT | LT | LT/ET |

### Q2: epoll的LT和ET模式区别？
**LT模式（水平触发）**：
- 只要缓冲区有数据就会通知
- 编程简单，不容易出错
- 效率相对较低

**ET模式（边缘触发）**：
- 只在状态变化时通知一次
- 必须一次性读完所有数据
- 效率高，但编程复杂

### Q3: 如何处理大量并发连接？
**技术方案**：
- 使用epoll进行I/O多路复用
- 采用线程池或进程池
- 实现连接池管理
- 使用非阻塞I/O
- 合理设置缓冲区大小

### Q4: Socket编程中的常见问题？
**TIME_WAIT状态**：
- 主动关闭方会进入TIME_WAIT状态
- 持续2MSL时间（通常2分钟）
- 可以设置SO_REUSEADDR选项

**粘包问题**：
- TCP是流协议，没有消息边界
- 解决方案：定长消息、分隔符、消息头+长度

### Q5: 如何优化网络程序性能？
**优化策略**：
- 使用零拷贝技术（sendfile、mmap）
- 启用TCP_NODELAY禁用Nagle算法
- 合理设置发送/接收缓冲区
- 使用连接池减少连接开销
- 实现负载均衡

## 面试要点总结

1. **Socket基础**：理解TCP/UDP编程流程和API使用
2. **I/O模型**：掌握select、poll、epoll的特点和适用场景
3. **并发处理**：了解多进程、多线程、事件驱动等并发模型
4. **性能优化**：掌握网络编程的性能优化技巧
5. **问题排查**：能够分析和解决常见的网络编程问题